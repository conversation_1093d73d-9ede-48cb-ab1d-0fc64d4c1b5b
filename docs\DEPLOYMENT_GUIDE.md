# PromoTun Deployment Guide

## Overview
This guide covers the deployment of the PromoTun application stack including the mobile app, backend API, merchant portal, and admin dashboard.

## Prerequisites

### System Requirements
- **Server**: Linux (Ubuntu 20.04+ recommended)
- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: Minimum 50GB SSD
- **CPU**: 2+ cores
- **Network**: Public IP with ports 80, 443 open

### Software Requirements
- Docker 20.10+
- Docker Compose 2.0+
- Node.js 18+ (for local development)
- PostgreSQL 15+ (if not using Docker)
- Redis 7+ (if not using Docker)
- Nginx (for reverse proxy)

## Environment Setup

### 1. Clone Repository
```bash
git clone https://github.com/your-org/promotun.git
cd promotun
```

### 2. Environment Configuration
Copy and configure environment files:

```bash
# Backend environment
cp backend/.env.example backend/.env
# Edit backend/.env with your configuration

# Deployment environment
cp deployment/.env.example deployment/.env
# Edit deployment/.env with your configuration
```

### 3. Required Environment Variables

#### Backend (.env)
```bash
# Database
DB_HOST=postgres
DB_PORT=5432
DB_NAME=promotun
DB_USER=postgres
DB_PASSWORD=your-secure-password

# Redis
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password

# JWT
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters

# Firebase (Push Notifications)
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_CLIENT_EMAIL=your-firebase-service-account-email
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"

# Email (SendGrid)
SENDGRID_API_KEY=your-sendgrid-api-key
FROM_EMAIL=<EMAIL>

# Google Maps
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
```

#### Deployment (.env)
```bash
# Database passwords
DB_PASSWORD=your-secure-database-password
REDIS_PASSWORD=your-secure-redis-password

# JWT Secret
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters

# API URLs
API_URL=https://api.yourdomain.com/api
SOCKET_URL=https://api.yourdomain.com

# SSL Configuration
SSL_EMAIL=<EMAIL>
DOMAIN=yourdomain.com

# Monitoring
GRAFANA_PASSWORD=your-grafana-password
```

## Deployment Methods

### Method 1: Docker Compose (Recommended)

#### 1. Build and Start Services
```bash
cd deployment
docker-compose up -d
```

#### 2. Initialize Database
```bash
# Run database migrations
docker-compose exec backend npm run migrate

# Seed initial data
docker-compose exec backend npm run seed
```

#### 3. Verify Deployment
```bash
# Check service status
docker-compose ps

# Check logs
docker-compose logs -f backend
docker-compose logs -f merchant-portal
```

### Method 2: Manual Deployment

#### 1. Database Setup
```bash
# Install PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# Create database and user
sudo -u postgres psql
CREATE DATABASE promotun;
CREATE USER promotun_user WITH PASSWORD 'your-password';
GRANT ALL PRIVILEGES ON DATABASE promotun TO promotun_user;
\q

# Run schema
psql -U promotun_user -d promotun -f database/schema.sql
```

#### 2. Redis Setup
```bash
# Install Redis
sudo apt install redis-server

# Configure Redis
sudo nano /etc/redis/redis.conf
# Set: requirepass your-redis-password

# Restart Redis
sudo systemctl restart redis-server
```

#### 3. Backend Deployment
```bash
cd backend
npm install --production
npm run build

# Install PM2 for process management
npm install -g pm2

# Start backend with PM2
pm2 start src/server.js --name "promotun-backend"
pm2 save
pm2 startup
```

#### 4. Frontend Deployment
```bash
# Merchant Portal
cd merchant-portal
npm install
npm run build
pm2 start npm --name "merchant-portal" -- start

# Admin Dashboard
cd ../admin-dashboard
npm install
npm run build
pm2 start npm --name "admin-dashboard" -- start
```

## Mobile App Deployment

### iOS Deployment

#### 1. Prerequisites
- Apple Developer Account
- Xcode 14+
- iOS Simulator or physical device

#### 2. Build Configuration
```bash
cd mobile-app

# Install dependencies
npm install

# Install iOS dependencies
cd ios && pod install && cd ..

# Configure app signing in Xcode
open ios/PromoTunMobile.xcworkspace
```

#### 3. Build and Deploy
```bash
# Build for App Store
npx expo build:ios --type archive

# Or use EAS Build
npx eas build --platform ios
```

### Android Deployment

#### 1. Prerequisites
- Android Studio
- Android SDK
- Java 11+

#### 2. Build Configuration
```bash
cd mobile-app

# Generate Android bundle
npx expo build:android --type app-bundle

# Or use EAS Build
npx eas build --platform android
```

## SSL Configuration

### Using Let's Encrypt with Certbot
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Generate SSL certificate
sudo certbot --nginx -d yourdomain.com -d api.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Nginx Configuration

### Main Configuration (/etc/nginx/sites-available/promotun)
```nginx
# API Server
server {
    listen 443 ssl http2;
    server_name api.yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;

    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}

# Merchant Portal
server {
    listen 443 ssl http2;
    server_name merchant.yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## Monitoring and Logging

### 1. Application Monitoring
```bash
# View application logs
docker-compose logs -f backend
docker-compose logs -f merchant-portal

# Monitor system resources
docker stats

# Check service health
curl -f http://localhost:5000/health
```

### 2. Database Monitoring
```bash
# PostgreSQL monitoring
docker-compose exec postgres psql -U postgres -c "SELECT * FROM pg_stat_activity;"

# Redis monitoring
docker-compose exec redis redis-cli info
```

### 3. Grafana Dashboard
Access Grafana at `http://localhost:3002`
- Username: admin
- Password: (set in environment variables)

## Backup and Recovery

### 1. Database Backup
```bash
# Create backup
docker-compose exec postgres pg_dump -U postgres promotun > backup_$(date +%Y%m%d_%H%M%S).sql

# Restore backup
docker-compose exec -T postgres psql -U postgres promotun < backup_file.sql
```

### 2. File Backup
```bash
# Backup uploaded files
tar -czf uploads_backup_$(date +%Y%m%d_%H%M%S).tar.gz backend/uploads/

# Backup configuration
tar -czf config_backup_$(date +%Y%m%d_%H%M%S).tar.gz deployment/.env backend/.env
```

## Scaling and Performance

### 1. Horizontal Scaling
```bash
# Scale backend instances
docker-compose up -d --scale backend=3

# Load balancer configuration required
```

### 2. Database Optimization
```sql
-- Create indexes for better performance
CREATE INDEX CONCURRENTLY idx_promotions_location ON promotions USING GIST (ST_Point(longitude, latitude));
CREATE INDEX CONCURRENTLY idx_promotions_dates ON promotions (start_date, end_date);
CREATE INDEX CONCURRENTLY idx_user_analytics_user_event ON user_analytics (user_id, event_type, created_at);
```

### 3. Redis Optimization
```bash
# Configure Redis for production
echo "maxmemory 2gb" >> /etc/redis/redis.conf
echo "maxmemory-policy allkeys-lru" >> /etc/redis/redis.conf
```

## Security Checklist

- [ ] Change all default passwords
- [ ] Enable firewall (UFW)
- [ ] Configure SSL certificates
- [ ] Set up regular security updates
- [ ] Enable database encryption
- [ ] Configure rate limiting
- [ ] Set up intrusion detection
- [ ] Regular security audits

## Troubleshooting

### Common Issues

#### 1. Database Connection Issues
```bash
# Check database status
docker-compose exec postgres pg_isready

# Check connection from backend
docker-compose exec backend npm run db:test
```

#### 2. Redis Connection Issues
```bash
# Test Redis connection
docker-compose exec redis redis-cli ping

# Check Redis logs
docker-compose logs redis
```

#### 3. SSL Certificate Issues
```bash
# Test SSL certificate
openssl s_client -connect yourdomain.com:443

# Renew certificate
sudo certbot renew --dry-run
```

## Maintenance

### Regular Tasks
- Monitor disk space and clean old logs
- Update Docker images monthly
- Backup database weekly
- Review security logs
- Update SSL certificates (automated)
- Monitor application performance
- Review and rotate API keys quarterly
