#!/bin/bash

# PromoTun Environment Setup and Validation Script
# This script validates and sets up environment variables for deployment

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEPLOYMENT_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_ROOT="$(dirname "$DEPLOYMENT_DIR")"

# Environment file paths
ENV_TEMPLATE="$DEPLOYMENT_DIR/.env.production.template"
ENV_FILE="$DEPLOYMENT_DIR/.env.production"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a variable is set and not empty
check_required_var() {
    local var_name="$1"
    local var_value="${!var_name:-}"
    
    if [[ -z "$var_value" ]]; then
        print_error "Required environment variable $var_name is not set or empty"
        return 1
    fi
    
    # Check for template placeholder values
    if [[ "$var_value" == *"CHANGE_ME"* ]]; then
        print_error "Environment variable $var_name still contains placeholder value: $var_value"
        return 1
    fi
    
    return 0
}

# Function to validate environment variables
validate_environment() {
    print_status "Validating environment variables..."
    
    local errors=0
    
    # Required variables
    local required_vars=(
        "DOMAIN_NAME"
        "DB_HOST"
        "DB_PORT"
        "DB_NAME"
        "DB_USER"
        "DB_PASSWORD"
        "REDIS_PASSWORD"
        "JWT_SECRET"
        "SESSION_SECRET"
        "NEXTAUTH_SECRET"
        "FIREBASE_PROJECT_ID"
        "FIREBASE_CLIENT_EMAIL"
        "FIREBASE_PRIVATE_KEY"
        "SENDGRID_API_KEY"
        "GOOGLE_MAPS_API_KEY"
        "FROM_EMAIL"
        "FROM_NAME"
        "GRAFANA_PASSWORD"
    )
    
    for var in "${required_vars[@]}"; do
        if ! check_required_var "$var"; then
            ((errors++))
        fi
    done
    
    # Validate specific formats
    if [[ -n "${JWT_SECRET:-}" && ${#JWT_SECRET} -lt 32 ]]; then
        print_error "JWT_SECRET must be at least 32 characters long"
        ((errors++))
    fi
    
    if [[ -n "${SESSION_SECRET:-}" && ${#SESSION_SECRET} -lt 32 ]]; then
        print_error "SESSION_SECRET must be at least 32 characters long"
        ((errors++))
    fi
    
    if [[ -n "${NEXTAUTH_SECRET:-}" && ${#NEXTAUTH_SECRET} -lt 32 ]]; then
        print_error "NEXTAUTH_SECRET must be at least 32 characters long"
        ((errors++))
    fi
    
    if [[ -n "${FROM_EMAIL:-}" && ! "$FROM_EMAIL" =~ ^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$ ]]; then
        print_error "FROM_EMAIL must be a valid email address"
        ((errors++))
    fi
    
    if [[ $errors -gt 0 ]]; then
        print_error "Environment validation failed with $errors errors"
        return 1
    fi
    
    print_success "Environment validation passed"
    return 0
}

# Function to create environment file from template
create_env_file() {
    print_status "Creating environment file from template..."
    
    if [[ ! -f "$ENV_TEMPLATE" ]]; then
        print_error "Environment template file not found: $ENV_TEMPLATE"
        return 1
    fi
    
    if [[ -f "$ENV_FILE" ]]; then
        print_warning "Environment file already exists: $ENV_FILE"
        read -p "Do you want to overwrite it? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_status "Keeping existing environment file"
            return 0
        fi
    fi
    
    cp "$ENV_TEMPLATE" "$ENV_FILE"
    print_success "Environment file created: $ENV_FILE"
    print_warning "Please edit $ENV_FILE and replace all placeholder values"
    return 0
}

# Function to generate secure secrets
generate_secrets() {
    print_status "Generating secure secrets..."
    
    echo "Generated secrets (save these securely):"
    echo "========================================"
    echo "JWT_SECRET=$(openssl rand -base64 64 | tr -d '\n')"
    echo "SESSION_SECRET=$(openssl rand -base64 64 | tr -d '\n')"
    echo "NEXTAUTH_SECRET=$(openssl rand -base64 64 | tr -d '\n')"
    echo "REDIS_PASSWORD=$(openssl rand -base64 32 | tr -d '\n')"
    echo "DB_PASSWORD=$(openssl rand -base64 32 | tr -d '\n')"
    echo "GRAFANA_PASSWORD=$(openssl rand -base64 16 | tr -d '\n')"
    echo "========================================"
}

# Function to test Docker Compose configuration
test_compose_config() {
    print_status "Testing Docker Compose configuration..."
    
    cd "$DEPLOYMENT_DIR"
    
    if ! docker-compose -f docker-compose.production.yml config > /dev/null; then
        print_error "Docker Compose configuration validation failed"
        return 1
    fi
    
    print_success "Docker Compose configuration is valid"
    return 0
}

# Main function
main() {
    print_status "PromoTun Environment Setup Script"
    print_status "================================="
    
    case "${1:-}" in
        "create")
            create_env_file
            ;;
        "validate")
            if [[ ! -f "$ENV_FILE" ]]; then
                print_error "Environment file not found: $ENV_FILE"
                print_status "Run '$0 create' to create it from template"
                exit 1
            fi
            
            # Load environment variables
            set -a
            source "$ENV_FILE"
            set +a
            
            validate_environment
            test_compose_config
            ;;
        "generate-secrets")
            generate_secrets
            ;;
        "help"|"--help"|"-h")
            echo "Usage: $0 {create|validate|generate-secrets|help}"
            echo ""
            echo "Commands:"
            echo "  create           Create .env.production from template"
            echo "  validate         Validate environment variables and Docker Compose config"
            echo "  generate-secrets Generate secure random secrets"
            echo "  help             Show this help message"
            ;;
        *)
            print_error "Invalid command: ${1:-}"
            echo "Run '$0 help' for usage information"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
