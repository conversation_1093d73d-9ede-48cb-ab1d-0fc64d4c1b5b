{"name": "promotun-merchant-portal", "version": "1.0.0", "description": "PromoTun Merchant Portal - Web interface for merchants to manage promotions", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"next": "13.4.12", "react": "18.2.0", "react-dom": "18.2.0", "@mui/material": "^5.14.1", "@mui/icons-material": "^5.14.1", "@mui/x-data-grid": "^6.10.1", "@mui/x-date-pickers": "^6.10.1", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^3.1.1", "react-hook-form": "^7.45.2", "yup": "^1.2.0", "axios": "^1.4.0", "react-query": "^3.39.3", "zustand": "^4.4.1", "recharts": "^2.7.2", "date-fns": "^2.30.0", "react-dropzone": "^14.2.3", "react-image-crop": "^10.1.8", "notistack": "^3.0.1", "next-auth": "^4.22.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.1", "socket.io-client": "^4.7.2", "i18next": "^23.2.11", "react-i18next": "^13.0.2", "i18next-browser-languagedetector": "^7.1.0", "react-beautiful-dnd": "^13.1.1", "react-helmet-async": "^1.3.0", "lodash": "^4.17.21", "uuid": "^9.0.0"}, "devDependencies": {"@types/node": "20.4.5", "@types/react": "18.2.17", "@types/react-dom": "18.2.7", "@types/lodash": "^4.14.195", "@types/uuid": "^9.0.2", "typescript": "5.1.6", "eslint": "8.45.0", "eslint-config-next": "13.4.12", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "jest": "^29.6.1", "jest-environment-jsdom": "^29.6.1", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^5.17.0", "prettier": "^3.0.0"}, "engines": {"node": ">=16.0.0"}}