# PromoTun Security Configuration
# Additional security settings for the PromoTun application

# Hide Nginx version
server_tokens off;

# Additional security headers (applied globally)
more_set_headers "X-Frame-Options: SAMEORIGIN";
more_set_headers "X-Content-Type-Options: nosniff";
more_set_headers "X-XSS-Protection: 1; mode=block";
more_set_headers "Referrer-Policy: strict-origin-when-cross-origin";

# Prevent access to hidden files
location ~ /\. {
    deny all;
    access_log off;
    log_not_found off;
}

# Prevent access to backup files
location ~ ~$ {
    deny all;
    access_log off;
    log_not_found off;
}

# Prevent access to configuration files
location ~ \.(htaccess|htpasswd|ini|log|sh|sql|conf)$ {
    deny all;
    access_log off;
    log_not_found off;
}

# Set client body size limit
client_max_body_size 10M;
client_body_buffer_size 128k;

# Timeout settings
client_header_timeout 10s;
client_body_timeout 10s;
send_timeout 10s;
keepalive_timeout 30s;

# Buffer settings
client_header_buffer_size 1k;
large_client_header_buffers 4 4k;
