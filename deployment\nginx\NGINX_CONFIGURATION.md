# PromoTun Nginx Configuration Guide

## Overview

The PromoTun deployment now uses a modular Nginx configuration structure that matches your existing dataxion VM setup. This provides better maintainability and follows standard Nginx practices.

## Configuration Structure

```
deployment/nginx/
├── nginx.production.conf          # Main nginx.conf (matches dataxion structure)
├── sites-enabled/
│   ├── promodetect.com            # PromoTun site configuration
│   └── promodetect.com.template   # Template for site configuration
├── conf.d/
│   └── promotun-security.conf     # Additional security settings
└── ssl/
    ├── promodetect.com.crt        # SSL certificate (created by setup-ssl.sh)
    ├── promodetect.com.key        # SSL private key (created by setup-ssl.sh)
    ├── dhparam.pem               # DH parameters (created by setup-ssl.sh)
    └── ssl-params.conf           # SSL configuration (created by setup-ssl.sh)
```

## Configuration Files

### 1. Main Configuration (`nginx.production.conf`)

Based on your dataxion VM structure with these key features:
- **User**: `www-data` (matches dataxion)
- **Worker processes**: `auto` (matches dataxion)
- **PID file**: `/run/nginx.pid` (matches dataxion)
- **Modules**: Includes `/etc/nginx/modules-enabled/*.conf`
- **Sites**: Includes `/etc/nginx/sites-enabled/*`
- **Additional configs**: Includes `/etc/nginx/conf.d/*.conf`

**PromoTun-specific additions:**
- Enhanced gzip configuration
- Security headers
- Rate limiting zones
- Upstream backend services definitions

### 2. Site Configuration (`sites-enabled/promodetect.com`)

Contains all PromoTun-specific server blocks:
- **HTTP to HTTPS redirect** (port 80)
- **HTTPS main server** (port 443)
- **SSL configuration** with modern ciphers
- **Reverse proxy rules** for all services
- **Security headers** and rate limiting
- **WebSocket support** for real-time features

**Service Routing:**
- `/api/` → Backend API (backend:5000)
- `/admin/` → Admin Dashboard (admin-dashboard:3000)
- `/prometheus/` → Prometheus (prometheus:9090)
- `/grafana/` → Grafana (grafana:3000)
- `/uploads/` → Static file serving
- `/` → Merchant Portal (merchant-portal:3000)

### 3. Security Configuration (`conf.d/promotun-security.conf`)

Additional security settings:
- Server tokens disabled
- Global security headers
- File access restrictions
- Timeout and buffer settings

## Docker Integration

### Volume Mounts

The Docker Compose configuration mounts:
```yaml
volumes:
  - ./nginx/nginx.production.conf:/etc/nginx/nginx.conf:ro
  - ./nginx/sites-enabled:/etc/nginx/sites-enabled:ro
  - ./nginx/ssl:/etc/nginx/ssl:ro
  - ./nginx/conf.d:/etc/nginx/conf.d:ro
  - backend_uploads:/var/www/uploads:ro
  - nginx_logs:/var/log/nginx
  - certbot_webroot:/var/www/certbot:ro
```

### Benefits of This Structure

1. **Compatibility**: Matches your existing dataxion VM nginx structure
2. **Modularity**: Separate files for different concerns
3. **Maintainability**: Easy to modify individual components
4. **Scalability**: Easy to add new sites or configurations
5. **Security**: Isolated configurations with proper permissions

## SSL Certificate Integration

### Certificate Paths
- **Certificate**: `/etc/nginx/ssl/promodetect.com.crt`
- **Private Key**: `/etc/nginx/ssl/promodetect.com.key`
- **DH Parameters**: `/etc/nginx/ssl/dhparam.pem`

### Let's Encrypt Integration
- **Webroot**: `/var/www/certbot` (mounted as volume)
- **ACME Challenge**: Handled in HTTP server block
- **Auto-renewal**: Configured in `setup-ssl.sh`

## Security Features

### Rate Limiting
```nginx
# API endpoints: 10 requests/second
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;

# Authentication: 1 request/second
limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

# Connection limiting: 20 concurrent connections
limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
```

### Security Headers
- **HSTS**: Strict Transport Security
- **CSP**: Content Security Policy
- **X-Frame-Options**: Clickjacking protection
- **X-Content-Type-Options**: MIME sniffing protection
- **X-XSS-Protection**: XSS filtering

### Access Control
- **Monitoring routes**: Basic auth protected
- **Hidden files**: Blocked (`.htaccess`, `.env`, etc.)
- **Sensitive files**: Blocked (`.ini`, `.conf`, etc.)

## Upstream Configuration

### Backend Services
```nginx
upstream backend {
    least_conn;
    server backend:5000 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

upstream merchant_portal {
    least_conn;
    server merchant-portal:3000 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

upstream admin_dashboard {
    least_conn;
    server admin-dashboard:3000 max_fails=3 fail_timeout=30s;
    keepalive 32;
}
```

### Load Balancing
- **Algorithm**: Least connections
- **Health checks**: 3 max failures, 30s timeout
- **Keep-alive**: 32 connections for performance

## Deployment Process

### 1. Initial Setup
```bash
# Application VM setup creates nginx directories
./deployment/scripts/setup-application-vm.sh
```

### 2. SSL Configuration
```bash
# SSL setup handles temporary nginx config
sudo ./deployment/scripts/setup-ssl.sh
```

### 3. Application Deployment
```bash
# Deployment script ensures configurations are in place
./deployment/scripts/deploy-production.sh
```

## Maintenance

### Configuration Testing
```bash
# Test nginx configuration
docker exec promotun-nginx nginx -t

# Reload nginx configuration
docker-compose -f docker-compose.production.yml restart nginx
```

### Log Monitoring
```bash
# Access logs
docker exec promotun-nginx tail -f /var/log/nginx/access.log

# Error logs
docker exec promotun-nginx tail -f /var/log/nginx/error.log
```

### SSL Certificate Renewal
```bash
# Manual renewal test
sudo /usr/local/bin/renew-ssl.sh

# Check renewal logs
sudo tail -f /var/log/ssl-renewal.log
```

## Troubleshooting

### Common Issues

#### 1. Configuration Syntax Errors
```bash
# Test configuration
docker exec promotun-nginx nginx -t

# Check error logs
docker logs promotun-nginx
```

#### 2. SSL Certificate Issues
```bash
# Check certificate validity
openssl x509 -in deployment/nginx/ssl/promodetect.com.crt -text -noout

# Verify certificate chain
openssl verify -CAfile deployment/nginx/ssl/promodetect.com.crt deployment/nginx/ssl/promodetect.com.crt
```

#### 3. Upstream Connection Issues
```bash
# Check backend connectivity
docker exec promotun-nginx curl -f http://backend:5000/health

# Check service status
docker-compose -f docker-compose.production.yml ps
```

### Performance Optimization

#### 1. Enable Additional Caching
Add to site configuration:
```nginx
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

#### 2. Enable Compression for More Types
Add to main configuration:
```nginx
gzip_types
    text/plain
    text/css
    text/xml
    text/javascript
    application/json
    application/javascript
    application/xml+rss
    application/atom+xml
    image/svg+xml
    application/x-font-ttf
    font/opentype;
```

## Migration from Previous Configuration

If you have an existing nginx configuration, follow these steps:

1. **Backup existing configuration**
2. **Copy main nginx.conf structure** from dataxion
3. **Move site-specific settings** to sites-enabled
4. **Update Docker Compose** volume mounts
5. **Test configuration** before deployment

This modular approach provides better maintainability while preserving all PromoTun functionality and security features.
