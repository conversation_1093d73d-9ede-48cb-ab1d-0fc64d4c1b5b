#!/bin/bash

echo ""
echo "========================================"
echo "  PromoTun Docker Deployment Script"
echo "========================================"
echo ""

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed or not in PATH"
    echo ""
    echo "💡 Please install Docker Desktop from:"
    echo "   https://www.docker.com/products/docker-desktop"
    echo ""
    exit 1
fi

echo "✅ Docker is installed"
docker --version

# Check if Docker daemon is running
if ! docker info &> /dev/null; then
    echo "❌ Docker daemon is not running"
    echo ""
    echo "💡 Please start Docker Desktop and try again"
    echo ""
    exit 1
fi

echo "✅ Docker daemon is running"
echo ""

# Navigate to deployment directory
if [ ! -d "deployment" ]; then
    echo "❌ Deployment directory not found"
    echo "   Please run this script from the PromoTun root directory"
    exit 1
fi

cd deployment

echo "🚀 Starting PromoTun Docker deployment..."
echo ""

# Stop any existing containers
echo "🧹 Cleaning up existing containers..."
docker-compose down -v &> /dev/null

# Start the complete stack
echo "🐳 Starting all services..."
if ! docker-compose up -d; then
    echo "❌ Failed to start Docker services"
    echo ""
    echo "🔧 Troubleshooting tips:"
    echo "   1. Check if ports 80, 3000, 3001, 5000, 5432, 6379 are available"
    echo "   2. Ensure Docker has enough memory allocated"
    echo "   3. Check Docker Desktop settings"
    echo ""
    exit 1
fi

echo ""
echo "⏳ Waiting for services to be ready..."
sleep 30

# Check service status
echo ""
echo "📊 Service Status:"
echo "=================="
docker-compose ps

echo ""
echo "🏥 Running health checks..."
echo ""

# Health check backend
echo "🔍 Checking Backend API..."
if curl -f -s http://localhost:5000/health > /dev/null 2>&1; then
    echo "✅ Backend API: Healthy"
else
    echo "❌ Backend API: Not responding"
fi

# Health check merchant portal
echo "🔍 Checking Merchant Portal..."
if curl -f -s http://localhost:3000/health > /dev/null 2>&1; then
    echo "✅ Merchant Portal: Healthy"
else
    echo "❌ Merchant Portal: Not responding"
fi

# Health check admin dashboard
echo "🔍 Checking Admin Dashboard..."
if curl -f -s http://localhost:3001/health > /dev/null 2>&1; then
    echo "✅ Admin Dashboard: Healthy"
else
    echo "❌ Admin Dashboard: Not responding"
fi

# Health check nginx
echo "🔍 Checking Nginx Proxy..."
if curl -f -s http://localhost/health > /dev/null 2>&1; then
    echo "✅ Nginx Proxy: Healthy"
else
    echo "❌ Nginx Proxy: Not responding"
fi

echo ""
echo "🎉 PromoTun Docker deployment completed!"
echo ""
echo "🌐 Service URLs:"
echo "================"
echo "🔧 Backend API:      http://localhost:5000"
echo "🏪 Merchant Portal:  http://localhost:3000"
echo "👑 Admin Dashboard:  http://localhost:3001"
echo "🌐 Main Application: http://localhost"
echo "📊 Grafana:          http://localhost:3002"
echo "📈 Prometheus:       http://localhost:9090"
echo ""
echo "🔑 Test Credentials:"
echo "===================="
echo "👤 Consumer: <EMAIL> / password"
echo "🏪 Merchant: <EMAIL> / password"
echo "👑 Admin:    <EMAIL> / password"
echo ""
echo "🛠️ Management Commands:"
echo "======================="
echo "📊 Status:   docker-compose ps"
echo "📋 Logs:     docker-compose logs -f"
echo "🛑 Stop:     docker-compose down"
echo "🔄 Restart:  docker-compose restart"
echo ""
echo "📖 For more information, see:"
echo "=============================="
echo "📄 DOCKER_DEPLOYMENT_REPORT.md"
echo "📄 QUICK_START_GUIDE.md"
echo ""

# Ask if user wants to view logs
read -p "Would you like to view live logs? (y/n): " choice
if [[ $choice == [Yy]* ]]; then
    echo ""
    echo "📋 Showing live logs (Press Ctrl+C to exit)..."
    docker-compose logs -f
fi

echo ""
echo "✅ PromoTun is now running!"
