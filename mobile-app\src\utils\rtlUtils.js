import { I18nManager } from 'react-native';
import * as Updates from 'expo-updates';
import i18n from '../locales/i18n';

// RTL languages
const RTL_LANGUAGES = ['ar', 'he', 'fa', 'ur'];

/**
 * Check if current language is RTL
 */
export const isRTL = (language = i18n.language) => {
  return RTL_LANGUAGES.includes(language);
};

/**
 * Get text alignment based on language direction
 */
export const getTextAlign = (language = i18n.language) => {
  return isRTL(language) ? 'right' : 'left';
};

/**
 * Get flex direction based on language direction
 */
export const getFlexDirection = (language = i18n.language) => {
  return isRTL(language) ? 'row-reverse' : 'row';
};

/**
 * Get margin/padding direction helpers
 */
export const getMarginStart = (value, language = i18n.language) => {
  return isRTL(language) ? { marginRight: value } : { marginLeft: value };
};

export const getMarginEnd = (value, language = i18n.language) => {
  return isRTL(language) ? { marginLeft: value } : { marginRight: value };
};

export const getPaddingStart = (value, language = i18n.language) => {
  return isRTL(language) ? { paddingRight: value } : { paddingLeft: value };
};

export const getPaddingEnd = (value, language = i18n.language) => {
  return isRTL(language) ? { paddingLeft: value } : { paddingRight: value };
};

/**
 * Get border radius for RTL support
 */
export const getBorderRadiusStart = (value, language = i18n.language) => {
  return isRTL(language) 
    ? { borderTopRightRadius: value, borderBottomRightRadius: value }
    : { borderTopLeftRadius: value, borderBottomLeftRadius: value };
};

export const getBorderRadiusEnd = (value, language = i18n.language) => {
  return isRTL(language) 
    ? { borderTopLeftRadius: value, borderBottomLeftRadius: value }
    : { borderTopRightRadius: value, borderBottomRightRadius: value };
};

/**
 * Get position helpers for RTL
 */
export const getPositionStart = (value, language = i18n.language) => {
  return isRTL(language) ? { right: value } : { left: value };
};

export const getPositionEnd = (value, language = i18n.language) => {
  return isRTL(language) ? { left: value } : { right: value };
};

/**
 * Transform style object for RTL support
 */
export const transformStyle = (style, language = i18n.language) => {
  if (!isRTL(language)) return style;

  const transformedStyle = { ...style };

  // Transform margin
  if (style.marginLeft !== undefined) {
    transformedStyle.marginRight = style.marginLeft;
    delete transformedStyle.marginLeft;
  }
  if (style.marginRight !== undefined) {
    transformedStyle.marginLeft = style.marginRight;
    delete transformedStyle.marginRight;
  }

  // Transform padding
  if (style.paddingLeft !== undefined) {
    transformedStyle.paddingRight = style.paddingLeft;
    delete transformedStyle.paddingLeft;
  }
  if (style.paddingRight !== undefined) {
    transformedStyle.paddingLeft = style.paddingRight;
    delete transformedStyle.paddingRight;
  }

  // Transform position
  if (style.left !== undefined) {
    transformedStyle.right = style.left;
    delete transformedStyle.left;
  }
  if (style.right !== undefined) {
    transformedStyle.left = style.right;
    delete transformedStyle.right;
  }

  // Transform border radius
  if (style.borderTopLeftRadius !== undefined) {
    transformedStyle.borderTopRightRadius = style.borderTopLeftRadius;
    delete transformedStyle.borderTopLeftRadius;
  }
  if (style.borderTopRightRadius !== undefined) {
    transformedStyle.borderTopLeftRadius = style.borderTopRightRadius;
    delete transformedStyle.borderTopRightRadius;
  }
  if (style.borderBottomLeftRadius !== undefined) {
    transformedStyle.borderBottomRightRadius = style.borderBottomLeftRadius;
    delete transformedStyle.borderBottomLeftRadius;
  }
  if (style.borderBottomRightRadius !== undefined) {
    transformedStyle.borderBottomLeftRadius = style.borderBottomRightRadius;
    delete transformedStyle.borderBottomRightRadius;
  }

  // Transform text align
  if (style.textAlign === 'left') {
    transformedStyle.textAlign = 'right';
  } else if (style.textAlign === 'right') {
    transformedStyle.textAlign = 'left';
  }

  return transformedStyle;
};

/**
 * Set RTL layout for the app
 */
export const setRTLLayout = async (language) => {
  const shouldBeRTL = isRTL(language);
  
  if (I18nManager.isRTL !== shouldBeRTL) {
    I18nManager.allowRTL(shouldBeRTL);
    I18nManager.forceRTL(shouldBeRTL);
    
    // Restart the app to apply RTL changes
    if (Updates.isAvailable) {
      await Updates.reloadAsync();
    }
  }
};

/**
 * Get icon name for RTL support (for directional icons)
 */
export const getDirectionalIcon = (iconName, language = i18n.language) => {
  if (!isRTL(language)) return iconName;

  const iconMap = {
    'arrow-left': 'arrow-right',
    'arrow-right': 'arrow-left',
    'chevron-left': 'chevron-right',
    'chevron-right': 'chevron-left',
    'keyboard-arrow-left': 'keyboard-arrow-right',
    'keyboard-arrow-right': 'keyboard-arrow-left',
    'navigate-before': 'navigate-next',
    'navigate-next': 'navigate-before',
  };

  return iconMap[iconName] || iconName;
};

/**
 * Format number for RTL languages
 */
export const formatNumber = (number, language = i18n.language) => {
  if (language === 'ar') {
    // Convert to Arabic-Indic numerals
    const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().replace(/\d/g, (digit) => arabicNumerals[parseInt(digit)]);
  }
  
  return number.toString();
};

/**
 * Format currency for different locales
 */
export const formatCurrency = (amount, language = i18n.language) => {
  const currencyMap = {
    en: { symbol: '$', position: 'before' },
    fr: { symbol: '€', position: 'after' },
    ar: { symbol: 'ر.س', position: 'after' },
  };

  const currency = currencyMap[language] || currencyMap.en;
  const formattedAmount = formatNumber(amount, language);

  return currency.position === 'before' 
    ? `${currency.symbol}${formattedAmount}`
    : `${formattedAmount} ${currency.symbol}`;
};

/**
 * Get writing direction for text input
 */
export const getWritingDirection = (language = i18n.language) => {
  return isRTL(language) ? 'rtl' : 'ltr';
};
