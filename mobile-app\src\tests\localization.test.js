import i18n from '../locales/i18n';
import { 
  isRTL, 
  getTextAlign, 
  getFlexDirection, 
  formatCurrency, 
  formatNumber,
  transformStyle 
} from '../utils/rtlUtils';

describe('Localization Tests', () => {
  describe('i18n Configuration', () => {
    test('should have all required languages', () => {
      const supportedLanguages = i18n.options.supportedLngs;
      expect(supportedLanguages).toContain('en');
      expect(supportedLanguages).toContain('fr');
      expect(supportedLanguages).toContain('ar');
    });

    test('should have fallback language', () => {
      expect(i18n.options.fallbackLng).toBe('en');
    });

    test('should load translation resources', () => {
      const resources = i18n.options.resources;
      expect(resources.en).toBeDefined();
      expect(resources.fr).toBeDefined();
      expect(resources.ar).toBeDefined();
    });
  });

  describe('Translation Keys', () => {
    const requiredKeys = [
      'navigation.home',
      'navigation.search',
      'navigation.favorites',
      'navigation.profile',
      'auth.login',
      'auth.register',
      'auth.email',
      'auth.password',
      'home.title',
      'common.ok',
      'common.cancel',
      'errors.networkError'
    ];

    test('should have all required keys in English', () => {
      i18n.changeLanguage('en');
      requiredKeys.forEach(key => {
        const translation = i18n.t(key);
        expect(translation).toBeDefined();
        expect(translation).not.toBe(key); // Should not return the key itself
      });
    });

    test('should have all required keys in French', () => {
      i18n.changeLanguage('fr');
      requiredKeys.forEach(key => {
        const translation = i18n.t(key);
        expect(translation).toBeDefined();
        expect(translation).not.toBe(key);
      });
    });

    test('should have all required keys in Arabic', () => {
      i18n.changeLanguage('ar');
      requiredKeys.forEach(key => {
        const translation = i18n.t(key);
        expect(translation).toBeDefined();
        expect(translation).not.toBe(key);
      });
    });

    test('should fallback to English for missing keys', () => {
      i18n.changeLanguage('fr');
      // Test with a key that might not exist in French
      const translation = i18n.t('nonexistent.key', { fallbackLng: 'en' });
      expect(translation).toBeDefined();
    });
  });

  describe('RTL Support', () => {
    test('should detect RTL languages correctly', () => {
      expect(isRTL('ar')).toBe(true);
      expect(isRTL('he')).toBe(true);
      expect(isRTL('en')).toBe(false);
      expect(isRTL('fr')).toBe(false);
    });

    test('should return correct text alignment', () => {
      expect(getTextAlign('ar')).toBe('right');
      expect(getTextAlign('en')).toBe('left');
      expect(getTextAlign('fr')).toBe('left');
    });

    test('should return correct flex direction', () => {
      expect(getFlexDirection('ar')).toBe('row-reverse');
      expect(getFlexDirection('en')).toBe('row');
      expect(getFlexDirection('fr')).toBe('row');
    });

    test('should transform styles for RTL', () => {
      const originalStyle = {
        marginLeft: 10,
        marginRight: 20,
        paddingLeft: 5,
        textAlign: 'left',
        left: 0
      };

      const transformedStyle = transformStyle(originalStyle, 'ar');

      expect(transformedStyle.marginRight).toBe(10);
      expect(transformedStyle.marginLeft).toBe(20);
      expect(transformedStyle.paddingRight).toBe(5);
      expect(transformedStyle.textAlign).toBe('right');
      expect(transformedStyle.right).toBe(0);
      expect(transformedStyle.left).toBeUndefined();
    });

    test('should not transform styles for LTR languages', () => {
      const originalStyle = {
        marginLeft: 10,
        textAlign: 'left'
      };

      const transformedStyle = transformStyle(originalStyle, 'en');

      expect(transformedStyle.marginLeft).toBe(10);
      expect(transformedStyle.textAlign).toBe('left');
    });
  });

  describe('Number and Currency Formatting', () => {
    test('should format numbers correctly for different locales', () => {
      expect(formatNumber(123, 'en')).toBe('123');
      expect(formatNumber(123, 'fr')).toBe('123');
      expect(formatNumber(123, 'ar')).toBe('١٢٣');
    });

    test('should format currency correctly for different locales', () => {
      expect(formatCurrency(100, 'en')).toBe('$100');
      expect(formatCurrency(100, 'fr')).toBe('100 €');
      expect(formatCurrency(100, 'ar')).toBe('١٠٠ ر.س');
    });
  });

  describe('Language Switching', () => {
    test('should change language dynamically', () => {
      i18n.changeLanguage('en');
      expect(i18n.t('common.ok')).toBe('OK');

      i18n.changeLanguage('fr');
      expect(i18n.t('common.ok')).toBe('OK'); // Same in French

      i18n.changeLanguage('ar');
      expect(i18n.t('common.ok')).toBe('موافق');
    });

    test('should maintain language preference', () => {
      i18n.changeLanguage('fr');
      expect(i18n.language).toBe('fr');
      
      i18n.changeLanguage('ar');
      expect(i18n.language).toBe('ar');
    });
  });

  describe('Interpolation', () => {
    test('should handle variable interpolation', () => {
      i18n.changeLanguage('en');
      
      // Assuming we have a key like "welcome": "Welcome, {{name}}!"
      const translation = i18n.t('dashboard.welcome', { name: 'John' });
      expect(translation).toContain('John');
    });

    test('should handle pluralization', () => {
      i18n.changeLanguage('en');
      
      // Test pluralization if implemented
      // This would depend on having plural forms in translation files
      const singular = i18n.t('items', { count: 1 });
      const plural = i18n.t('items', { count: 5 });
      
      expect(singular).toBeDefined();
      expect(plural).toBeDefined();
    });
  });

  describe('Context-aware Translations', () => {
    test('should handle context-specific translations', () => {
      i18n.changeLanguage('en');
      
      // Test different contexts for the same word
      const buttonSave = i18n.t('common.save');
      const menuSave = i18n.t('common.save');
      
      expect(buttonSave).toBeDefined();
      expect(menuSave).toBeDefined();
    });

    test('should handle gender-specific translations for Arabic', () => {
      i18n.changeLanguage('ar');
      
      // Arabic often has gender-specific forms
      // This would test if such forms are properly handled
      const translation = i18n.t('auth.loginSuccess');
      expect(translation).toBeDefined();
      expect(translation.length).toBeGreaterThan(0);
    });
  });

  describe('Date and Time Formatting', () => {
    test('should format dates according to locale', () => {
      const testDate = new Date('2023-07-15T10:30:00Z');
      
      // English format
      const enFormat = testDate.toLocaleDateString('en-US');
      expect(enFormat).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}/);
      
      // French format
      const frFormat = testDate.toLocaleDateString('fr-FR');
      expect(frFormat).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}/);
      
      // Arabic format
      const arFormat = testDate.toLocaleDateString('ar-SA');
      expect(arFormat).toBeDefined();
    });

    test('should format times according to locale', () => {
      const testDate = new Date('2023-07-15T10:30:00Z');
      
      const enTime = testDate.toLocaleTimeString('en-US');
      const frTime = testDate.toLocaleTimeString('fr-FR');
      const arTime = testDate.toLocaleTimeString('ar-SA');
      
      expect(enTime).toBeDefined();
      expect(frTime).toBeDefined();
      expect(arTime).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    test('should handle missing translation gracefully', () => {
      i18n.changeLanguage('en');
      
      const missingKey = i18n.t('nonexistent.deeply.nested.key');
      expect(missingKey).toBeDefined();
      // Should either return the key or a fallback
    });

    test('should handle invalid language codes', () => {
      const originalLanguage = i18n.language;
      
      try {
        i18n.changeLanguage('invalid-lang');
        // Should fallback to default language
        expect(i18n.language).toBe('en');
      } catch (error) {
        // Or handle error gracefully
        expect(error).toBeDefined();
      }
      
      // Restore original language
      i18n.changeLanguage(originalLanguage);
    });
  });
});
