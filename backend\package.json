{"name": "promotun-backend", "version": "1.0.0", "description": "PromoTun Backend API Server", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --coverage --ci --watchAll=false --passWithNoTests", "migrate": "node src/database/migrate.js", "seed": "node src/database/seed.js", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"axios": "^1.10.0", "bcryptjs": "^2.4.3", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^6.11.2", "express-validator": "^7.2.1", "firebase-admin": "^13.4.0", "geolib": "^3.3.4", "helmet": "^7.2.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.10.1", "pg": "^8.16.2", "redis": "^4.7.1", "sharp": "^0.32.6", "socket.io": "^4.8.1", "uuid": "^9.0.1", "winston": "^3.17.0"}, "devDependencies": {"eslint": "^8.57.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.27.5", "jest": "^29.7.0", "jest-junit": "^16.0.0", "nodemon": "^3.1.10", "supertest": "^6.3.4"}, "engines": {"node": ">=16.0.0"}, "keywords": ["promotions", "deals", "mobile-app", "api", "nodejs", "express"], "author": "PromoTun Team", "license": "UNLICENSED"}