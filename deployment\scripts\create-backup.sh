#!/bin/bash

# PromoTun Backup Creation Script
# Creates comprehensive backup before deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKUP_NAME=${1:-manual-$(date +%Y%m%d_%H%M%S)}
BACKUP_DIR="/opt/promotun/backups"
APP_DIR="/opt/promotun"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Create backup directory
create_backup_dir() {
    print_info "Creating backup directory..."
    
    mkdir -p ${BACKUP_DIR}/${BACKUP_NAME}
    
    print_status "Backup directory created: ${BACKUP_DIR}/${BACKUP_NAME}"
}

# Backup Docker volumes
backup_volumes() {
    print_info "Backing up Docker volumes..."
    
    # Check if containers are running
    if docker-compose -f docker-compose.production.yml ps | grep -q "Up"; then
        # Backup backend uploads
        docker run --rm \
            -v promotun_backend_uploads:/data \
            -v ${BACKUP_DIR}/${BACKUP_NAME}:/backup \
            alpine tar czf /backup/backend_uploads.tar.gz -C /data .
        
        # Backup backend logs
        docker run --rm \
            -v promotun_backend_logs:/data \
            -v ${BACKUP_DIR}/${BACKUP_NAME}:/backup \
            alpine tar czf /backup/backend_logs.tar.gz -C /data .
        
        # Backup Grafana data
        docker run --rm \
            -v promotun_grafana_data:/data \
            -v ${BACKUP_DIR}/${BACKUP_NAME}:/backup \
            alpine tar czf /backup/grafana_data.tar.gz -C /data .
        
        # Backup Prometheus data
        docker run --rm \
            -v promotun_prometheus_data:/data \
            -v ${BACKUP_DIR}/${BACKUP_NAME}:/backup \
            alpine tar czf /backup/prometheus_data.tar.gz -C /data .
        
        # Backup Redis data
        docker run --rm \
            -v promotun_redis_data:/data \
            -v ${BACKUP_DIR}/${BACKUP_NAME}:/backup \
            alpine tar czf /backup/redis_data.tar.gz -C /data .
        
        print_status "Docker volumes backed up"
    else
        print_warning "No running containers found, skipping volume backup"
    fi
}

# Backup configuration files
backup_configs() {
    print_info "Backing up configuration files..."
    
    # Backup deployment configurations
    tar czf ${BACKUP_DIR}/${BACKUP_NAME}/deployment_configs.tar.gz \
        -C ${APP_DIR} \
        deployment/docker-compose.production.yml \
        deployment/.env.production \
        deployment/nginx/ \
        deployment/monitoring/ \
        2>/dev/null || true
    
    # Backup SSL certificates
    if [[ -d "${APP_DIR}/ssl" ]]; then
        tar czf ${BACKUP_DIR}/${BACKUP_NAME}/ssl_certificates.tar.gz \
            -C ${APP_DIR} ssl/ 2>/dev/null || true
    fi
    
    print_status "Configuration files backed up"
}

# Backup database (if accessible)
backup_database() {
    print_info "Backing up database..."
    
    # Source environment variables
    if [[ -f "${APP_DIR}/deployment/.env.production" ]]; then
        source ${APP_DIR}/deployment/.env.production
        
        # Create database backup
        if command -v pg_dump &> /dev/null; then
            PGPASSWORD=${DB_PASSWORD} pg_dump \
                -h ${DB_HOST} \
                -U ${DB_USER} \
                -d ${DB_NAME} \
                --no-password \
                --verbose \
                --format=custom \
                --file=${BACKUP_DIR}/${BACKUP_NAME}/database_backup.dump \
                2>/dev/null || print_warning "Database backup failed - continuing without it"
            
            if [[ -f "${BACKUP_DIR}/${BACKUP_NAME}/database_backup.dump" ]]; then
                print_status "Database backed up"
            fi
        else
            print_warning "pg_dump not available, skipping database backup"
        fi
    else
        print_warning "Environment file not found, skipping database backup"
    fi
}

# Backup application code
backup_code() {
    print_info "Backing up application code..."
    
    # Get current git commit
    if [[ -d "${APP_DIR}/.git" ]]; then
        cd ${APP_DIR}
        git rev-parse HEAD > ${BACKUP_DIR}/${BACKUP_NAME}/git_commit.txt
        git status --porcelain > ${BACKUP_DIR}/${BACKUP_NAME}/git_status.txt
        
        # Backup any uncommitted changes
        if [[ -s "${BACKUP_DIR}/${BACKUP_NAME}/git_status.txt" ]]; then
            git diff > ${BACKUP_DIR}/${BACKUP_NAME}/uncommitted_changes.patch
        fi
        
        print_status "Application code state backed up"
    else
        print_warning "Not a git repository, skipping code backup"
    fi
}

# Create backup manifest
create_manifest() {
    print_info "Creating backup manifest..."
    
    cat > ${BACKUP_DIR}/${BACKUP_NAME}/backup_manifest.txt << EOF
PromoTun Backup Manifest
========================

Backup Name: ${BACKUP_NAME}
Created: $(date)
Created By: $(whoami)
Hostname: $(hostname)

Contents:
EOF
    
    # List backup contents
    find ${BACKUP_DIR}/${BACKUP_NAME} -type f -exec basename {} \; | sort >> ${BACKUP_DIR}/${BACKUP_NAME}/backup_manifest.txt
    
    # Add file sizes
    echo "" >> ${BACKUP_DIR}/${BACKUP_NAME}/backup_manifest.txt
    echo "File Sizes:" >> ${BACKUP_DIR}/${BACKUP_NAME}/backup_manifest.txt
    du -h ${BACKUP_DIR}/${BACKUP_NAME}/* >> ${BACKUP_DIR}/${BACKUP_NAME}/backup_manifest.txt
    
    print_status "Backup manifest created"
}

# Compress final backup
compress_backup() {
    print_info "Compressing backup..."
    
    cd ${BACKUP_DIR}
    tar czf ${BACKUP_NAME}.tar.gz ${BACKUP_NAME}/
    
    if [[ $? -eq 0 ]]; then
        rm -rf ${BACKUP_NAME}/
        print_status "Backup compressed: ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz"
    else
        print_error "Failed to compress backup"
        exit 1
    fi
}

# Clean old backups
clean_old_backups() {
    print_info "Cleaning old backups..."
    
    # Keep last 10 backups
    cd ${BACKUP_DIR}
    ls -t *.tar.gz 2>/dev/null | tail -n +11 | xargs rm -f 2>/dev/null || true
    
    print_status "Old backups cleaned"
}

# Show backup summary
show_summary() {
    print_info "Backup Summary"
    echo "=============="
    echo
    
    echo "📦 Backup Details:"
    echo "   Name: ${BACKUP_NAME}"
    echo "   Location: ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz"
    echo "   Size: $(du -h ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz | cut -f1)"
    echo "   Created: $(date)"
    echo
    
    echo "📋 Backup Contents:"
    tar -tzf ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz | head -20
    echo
    
    if [[ $(tar -tzf ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz | wc -l) -gt 20 ]]; then
        echo "   ... and $(( $(tar -tzf ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz | wc -l) - 20 )) more files"
        echo
    fi
    
    echo "🔄 Restore Command:"
    echo "   cd ${BACKUP_DIR} && tar xzf ${BACKUP_NAME}.tar.gz"
    echo
}

# Main execution
main() {
    print_info "Starting PromoTun backup creation..."
    print_info "Backup name: ${BACKUP_NAME}"
    echo
    
    create_backup_dir
    backup_volumes
    backup_configs
    backup_database
    backup_code
    create_manifest
    compress_backup
    clean_old_backups
    show_summary
    
    print_status "Backup creation completed successfully! 🎉"
    echo
    print_info "Backup saved: ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz"
}

# Run main function
main "$@"
