const { query, transaction } = require('../database/connection');

describe('Database Schema and Integrity Tests', () => {
  describe('Table Structure Tests', () => {
    test('should have all required tables', async () => {
      const expectedTables = [
        'users', 'user_profiles', 'merchant_profiles', 'business_locations',
        'categories', 'promotions', 'promotion_images', 'user_favorites',
        'user_feedback', 'merchant_responses', 'notifications', 'user_analytics'
      ];

      for (const tableName of expectedTables) {
        const result = await query(
          "SELECT table_name FROM information_schema.tables WHERE table_name = $1",
          [tableName]
        );
        expect(result.rows.length).toBe(1);
      }
    });

    test('should have proper primary keys', async () => {
      const result = await query(`
        SELECT table_name, column_name 
        FROM information_schema.key_column_usage 
        WHERE constraint_name LIKE '%_pkey'
        AND table_schema = 'public'
      `);

      const primaryKeys = result.rows.reduce((acc, row) => {
        acc[row.table_name] = row.column_name;
        return acc;
      }, {});

      expect(primaryKeys.users).toBe('id');
      expect(primaryKeys.promotions).toBe('id');
      expect(primaryKeys.categories).toBe('id');
    });

    test('should have proper foreign key constraints', async () => {
      const result = await query(`
        SELECT 
          tc.table_name, 
          kcu.column_name, 
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name 
        FROM information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_schema = 'public'
      `);

      expect(result.rows.length).toBeGreaterThan(0);
      
      // Check specific foreign keys
      const foreignKeys = result.rows;
      const userProfileFK = foreignKeys.find(fk => 
        fk.table_name === 'user_profiles' && fk.column_name === 'user_id'
      );
      expect(userProfileFK.foreign_table_name).toBe('users');
    });
  });

  describe('Data Integrity Tests', () => {
    let testUserId;
    let testCategoryId;

    beforeAll(async () => {
      // Create test data
      const userResult = await query(`
        INSERT INTO users (email, password_hash, first_name, last_name, user_type)
        VALUES ('<EMAIL>', '$2b$12$test', 'DB', 'Test', 'consumer')
        RETURNING id
      `);
      testUserId = userResult.rows[0].id;

      const categoryResult = await query(`
        SELECT id FROM categories LIMIT 1
      `);
      testCategoryId = categoryResult.rows[0]?.id;
    });

    afterAll(async () => {
      // Cleanup
      if (testUserId) {
        await query('DELETE FROM users WHERE id = $1', [testUserId]);
      }
    });

    test('should enforce unique email constraint', async () => {
      try {
        await query(`
          INSERT INTO users (email, password_hash, first_name, last_name, user_type)
          VALUES ('<EMAIL>', '$2b$12$test', 'DB', 'Test2', 'consumer')
        `);
        fail('Should have thrown unique constraint error');
      } catch (error) {
        expect(error.code).toBe('23505'); // Unique violation
      }
    });

    test('should enforce check constraints', async () => {
      try {
        await query(`
          INSERT INTO users (email, password_hash, first_name, last_name, user_type)
          VALUES ('<EMAIL>', '$2b$12$test', 'Invalid', 'User', 'invalid_type')
        `);
        fail('Should have thrown check constraint error');
      } catch (error) {
        expect(error.code).toBe('23514'); // Check violation
      }
    });

    test('should cascade deletes properly', async () => {
      // Create user profile
      await query(`
        INSERT INTO user_profiles (user_id)
        VALUES ($1)
      `, [testUserId]);

      // Verify profile exists
      const profileBefore = await query(
        'SELECT id FROM user_profiles WHERE user_id = $1',
        [testUserId]
      );
      expect(profileBefore.rows.length).toBe(1);

      // Delete user
      await query('DELETE FROM users WHERE id = $1', [testUserId]);

      // Verify profile was cascaded
      const profileAfter = await query(
        'SELECT id FROM user_profiles WHERE user_id = $1',
        [testUserId]
      );
      expect(profileAfter.rows.length).toBe(0);

      testUserId = null; // Prevent cleanup
    });

    test('should handle transactions properly', async () => {
      const testEmail = '<EMAIL>';
      
      try {
        await transaction(async (client) => {
          // Insert user
          const userResult = await client.query(`
            INSERT INTO users (email, password_hash, first_name, last_name, user_type)
            VALUES ($1, '$2b$12$test', 'Trans', 'Test', 'consumer')
            RETURNING id
          `, [testEmail]);

          const userId = userResult.rows[0].id;

          // Insert profile
          await client.query(`
            INSERT INTO user_profiles (user_id)
            VALUES ($1)
          `, [userId]);

          // Simulate error
          throw new Error('Simulated error');
        });
      } catch (error) {
        expect(error.message).toBe('Simulated error');
      }

      // Verify rollback - user should not exist
      const userCheck = await query(
        'SELECT id FROM users WHERE email = $1',
        [testEmail]
      );
      expect(userCheck.rows.length).toBe(0);
    });
  });

  describe('Index Performance Tests', () => {
    test('should have indexes on frequently queried columns', async () => {
      const result = await query(`
        SELECT 
          schemaname,
          tablename,
          indexname,
          indexdef
        FROM pg_indexes 
        WHERE schemaname = 'public'
        ORDER BY tablename, indexname
      `);

      const indexes = result.rows.map(row => ({
        table: row.tablename,
        index: row.indexname,
        definition: row.indexdef
      }));

      // Check for important indexes
      const emailIndex = indexes.find(idx => 
        idx.table === 'users' && idx.definition.includes('email')
      );
      expect(emailIndex).toBeDefined();

      const locationIndex = indexes.find(idx => 
        idx.table === 'business_locations' && 
        (idx.definition.includes('latitude') || idx.definition.includes('longitude'))
      );
      expect(locationIndex).toBeDefined();
    });

    test('should have efficient query plans for common queries', async () => {
      // Test query plan for user lookup by email
      const emailPlan = await query(`
        EXPLAIN (FORMAT JSON) 
        SELECT id FROM users WHERE email = '<EMAIL>'
      `);

      const plan = emailPlan.rows[0]['QUERY PLAN'][0];
      expect(plan.Plan['Node Type']).toBe('Index Scan');
    });
  });

  describe('Data Validation Tests', () => {
    test('should validate email format at application level', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      const invalidEmails = [
        'invalid-email',
        '@domain.com',
        'user@',
        'user <EMAIL>'
      ];

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

      validEmails.forEach(email => {
        expect(emailRegex.test(email)).toBe(true);
      });

      invalidEmails.forEach(email => {
        expect(emailRegex.test(email)).toBe(false);
      });
    });

    test('should validate password strength requirements', () => {
      const validPasswords = [
        'StrongPass123!',
        'MySecure@Pass1',
        'Complex#Password9'
      ];

      const invalidPasswords = [
        'weak',
        'nouppercaseornumbers',
        'NOLOWERCASEORNUMBERS',
        'NoNumbers!',
        'NoSpecialChars123'
      ];

      // Password should have: min 8 chars, uppercase, lowercase, number
      const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/;

      validPasswords.forEach(password => {
        expect(passwordRegex.test(password)).toBe(true);
      });

      invalidPasswords.forEach(password => {
        expect(passwordRegex.test(password)).toBe(false);
      });
    });
  });

  describe('Trigger Tests', () => {
    test('should update updated_at timestamp on record update', async () => {
      // Create test user
      const userResult = await query(`
        INSERT INTO users (email, password_hash, first_name, last_name, user_type)
        VALUES ('<EMAIL>', '$2b$12$test', 'Trigger', 'Test', 'consumer')
        RETURNING id, updated_at
      `);

      const userId = userResult.rows[0].id;
      const originalTimestamp = userResult.rows[0].updated_at;

      // Wait a moment to ensure timestamp difference
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update user
      await query(`
        UPDATE users SET first_name = 'Updated' WHERE id = $1
      `, [userId]);

      // Check updated timestamp
      const updatedResult = await query(`
        SELECT updated_at FROM users WHERE id = $1
      `, [userId]);

      const newTimestamp = updatedResult.rows[0].updated_at;
      expect(new Date(newTimestamp)).toBeInstanceOf(Date);
      expect(new Date(newTimestamp).getTime()).toBeGreaterThan(new Date(originalTimestamp).getTime());

      // Cleanup
      await query('DELETE FROM users WHERE id = $1', [userId]);
    });
  });
});
