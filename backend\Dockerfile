# PromoTun Backend API - Multi-stage Production Dockerfile
# Node.js Express API with PostgreSQL and Redis integration

# ===================================
# Base Stage - Common dependencies
# ===================================
FROM node:18-alpine AS base

# Install system dependencies and security updates
RUN apk update && apk upgrade && \
    apk add --no-cache \
    dumb-init \
    curl \
    postgresql-client \
    && rm -rf /var/cache/apk/*

# Create app user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S promotun -u 1001 -G nodejs

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# ===================================
# Development Stage
# ===================================
FROM base AS development

# Install all dependencies (including dev dependencies)
RUN npm ci --include=dev

# Copy source code
COPY . .

# Change ownership to app user
RUN chown -R promotun:nodejs /app

# Switch to non-root user
USER promotun

# Expose development port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# Development command
CMD ["dumb-init", "npm", "run", "dev"]

# ===================================
# Dependencies Stage - Production dependencies only
# ===================================
FROM base AS dependencies

# Set NODE_ENV to production for optimal npm install
ENV NODE_ENV=production

# Install only production dependencies
RUN npm ci --only=production --no-audit --no-fund && \
    npm cache clean --force

# ===================================
# Build Stage - Compile TypeScript and prepare assets
# ===================================
FROM base AS build

# Install all dependencies for building
RUN npm ci --include=dev

# Copy source code
COPY . .

# Build the application (if using TypeScript or build process)
RUN npm run build 2>/dev/null || echo "No build script found, skipping..." && \
    npm prune --production

# ===================================
# Production Stage - Final optimized image
# ===================================
FROM node:18-alpine AS production

# Install runtime dependencies and security updates
RUN apk update && apk upgrade && \
    apk add --no-cache \
    curl \
    dumb-init \
    postgresql-client \
    && rm -rf /var/cache/apk/*

# Create app user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S promotun -u 1001 -G nodejs

# Set working directory
WORKDIR /app

# Set production environment
ENV NODE_ENV=production
ENV PORT=5000

# Copy production dependencies from dependencies stage
COPY --from=dependencies --chown=promotun:nodejs /app/node_modules ./node_modules

# Copy built application from build stage
COPY --from=build --chown=promotun:nodejs /app/dist ./dist 2>/dev/null || \
COPY --chown=promotun:nodejs . .

# Create necessary directories
RUN mkdir -p logs uploads && \
    chown -R promotun:nodejs logs uploads

# Remove unnecessary files for production
RUN rm -rf \
    .git \
    .gitignore \
    .dockerignore \
    README.md \
    Dockerfile \
    docker-compose*.yml \
    .env.example \
    tests \
    test \
    __tests__ \
    *.test.js \
    *.spec.js \
    coverage \
    .nyc_output

# Switch to non-root user
USER promotun

# Expose application port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# Production command with proper signal handling
CMD ["dumb-init", "node", "src/server.js"]

# ===================================
# Metadata
# ===================================
LABEL maintainer="PromoTun Development Team"
LABEL version="1.0.0"
LABEL description="PromoTun Backend API - Production Ready"
LABEL org.opencontainers.image.source="https://gitlab.com/promotun/promotun-platform"
