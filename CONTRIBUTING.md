# Contributing to <PERSON><PERSON><PERSON><PERSON>

Thank you for your interest in contributing to PromoTun! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites
- Node.js 16 or higher
- Docker Desktop (for full stack development)
- Git
- A code editor (VS Code recommended)

### Development Environment Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd PromoTun
   ```

2. **Install dependencies**
   ```bash
   # Backend
   cd backend
   npm install

   # Merchant Portal
   cd ../merchant-portal
   npm install

   # Admin Dashboard
   cd ../admin-dashboard
   npm install
   ```

3. **Configure environment variables**
   ```bash
   # Copy example files and configure
   cp backend/.env.example backend/.env
   cp merchant-portal/.env.example merchant-portal/.env.local
   cp deployment/.env.example deployment/.env
   ```

4. **Start development servers**
   ```bash
   # Backend (Terminal 1)
   cd backend
   npm run dev

   # Merchant Portal (Terminal 2)
   cd merchant-portal
   npm run dev
   ```

## 📋 Development Guidelines

### Code Style
- Use TypeScript for new components
- Follow ESLint and Prettier configurations
- Use meaningful variable and function names
- Add JSDoc comments for complex functions

### Git Workflow
1. Create a feature branch from `main`
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. Make your changes with clear, atomic commits
   ```bash
   git commit -m "feat: add user authentication middleware"
   ```

3. Push your branch and create a Pull Request
   ```bash
   git push origin feature/your-feature-name
   ```

### Commit Message Convention
We follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation changes
- `style:` - Code style changes (formatting, etc.)
- `refactor:` - Code refactoring
- `test:` - Adding or updating tests
- `chore:` - Maintenance tasks

### Branch Naming
- `feature/` - New features
- `fix/` - Bug fixes
- `docs/` - Documentation updates
- `refactor/` - Code refactoring
- `test/` - Test improvements

## 🧪 Testing

### Running Tests
```bash
# Backend tests
cd backend
npm test

# Frontend tests
cd merchant-portal
npm test
```

### Writing Tests
- Write unit tests for new functions
- Add integration tests for API endpoints
- Include component tests for React components
- Ensure tests pass before submitting PR

## 📝 Documentation

### Code Documentation
- Add JSDoc comments for public functions
- Update README files when adding new features
- Document API endpoints in the docs/ directory

### API Documentation
- Update OpenAPI/Swagger specifications
- Include request/response examples
- Document error codes and responses

## 🐛 Bug Reports

When reporting bugs, please include:
- Clear description of the issue
- Steps to reproduce
- Expected vs actual behavior
- Environment details (OS, Node.js version, etc.)
- Screenshots or error logs if applicable

## 💡 Feature Requests

For new features:
- Describe the use case and problem it solves
- Provide mockups or examples if applicable
- Consider backward compatibility
- Discuss implementation approach

## 🔍 Code Review Process

### Before Submitting
- [ ] Code follows style guidelines
- [ ] Tests pass locally
- [ ] Documentation is updated
- [ ] No console.log statements in production code
- [ ] Environment variables are properly configured

### Review Criteria
- Code quality and readability
- Performance implications
- Security considerations
- Test coverage
- Documentation completeness

## 🚀 Deployment

### Development Deployment
- Changes are automatically deployed to development environment
- Test your changes thoroughly before merging

### Production Deployment
- Only maintainers can deploy to production
- All changes must be reviewed and approved
- Deployment follows the release process

## 📞 Getting Help

- Create an issue for bugs or feature requests
- Join our development chat for questions
- Check existing documentation and issues first

## 🏆 Recognition

Contributors will be recognized in:
- CONTRIBUTORS.md file
- Release notes for significant contributions
- Project documentation

Thank you for contributing to PromoTun! 🎉
