# PromoTun - Comprehensive Promotion Discovery Platform

[![License](https://img.shields.io/badge/license-Proprietary-red.svg)](LICENSE)
[![Node.js](https://img.shields.io/badge/node.js-16%2B-green.svg)](https://nodejs.org/)
[![React Native](https://img.shields.io/badge/react--native-0.72-blue.svg)](https://reactnative.dev/)
[![Docker](https://img.shields.io/badge/docker-ready-blue.svg)](https://docker.com/)

## Overview
PromoTun is a comprehensive mobile application and web platform that connects consumers with local promotions, discounts, and deals while providing merchants with powerful tools to manage and promote their offers.

## 🚀 Quick Start

### Prerequisites
- Node.js 16+
- Docker Desktop (for full deployment)
- PostgreSQL (for local development)
- Redis (for caching)

### Current Status
- ✅ **Backend API**: Fully functional (http://localhost:5000)
- ⚠️ **Merchant Portal**: SWC compilation issues (Next.js)
- ⚠️ **Admin Dashboard**: In development
- 📱 **Mobile App**: React Native structure ready
- 🐳 **Docker**: Configuration ready, startup issues on some systems

### Quick Setup
```bash
# Clone the repository
git clone <repository-url>
cd PromoTun

# Start the backend (working)
cd backend
npm install
npm start

# Backend will be available at http://localhost:5000
```

## Architecture Overview

### Technology Stack
- **Mobile App**: React Native (iOS & Android)
- **Backend**: Node.js with Express.js
- **Database**: PostgreSQL with Redis for caching
- **Web Portal**: Next.js with TypeScript
- **Real-time**: Socket.io for live updates
- **Notifications**: Firebase Cloud Messaging (FCM)
- **File Storage**: Local uploads (configurable for AWS S3)
- **Deployment**: Docker containers with Nginx

### Project Structure
```
PromoTun/
├── backend/             # Node.js API server (✅ Working)
│   ├── src/
│   │   ├── routes/      # API endpoints
│   │   ├── controllers/ # Business logic
│   │   ├── middleware/  # Authentication, validation
│   │   ├── services/    # External integrations
│   │   └── utils/       # Helper functions
│   ├── .env.example     # Environment template
│   └── Dockerfile       # Container configuration
├── merchant-portal/     # Next.js web portal (⚠️ SWC issues)
│   ├── src/
│   │   ├── pages/       # Next.js pages
│   │   ├── components/  # React components
│   │   └── store/       # State management
│   ├── .env.example     # Environment template
│   └── Dockerfile       # Container configuration
├── admin-dashboard/     # Admin management interface
├── mobile-app/          # React Native application
├── deployment/          # Docker compose configurations
│   ├── docker-compose.yml
│   ├── .env.example     # Deployment environment template
│   └── nginx/           # Reverse proxy configuration
├── database/            # Database schemas and migrations
├── docs/                # API documentation
└── shared/              # Shared utilities and types
```

## 🎯 Core Features

### 1. Automated Promotion Detection System
- Real-time aggregation of promotions across multiple sources
- Geographic filtering based on user location
- Machine learning for promotion categorization

### 2. Categorized Results Display
- Organized product categories
- Price comparison functionality
- Advanced search and filtering

### 3. Smart Notification System
- Personalized push notifications
- Location-based alerts
- User preference-driven targeting

### 4. User Personalization
- Favorites management
- Preference settings
- Browsing history tracking

### 5. Merchant Portal
- Promotion management interface
- Analytics dashboard with Material-UI components
- Customer feedback system

### 6. Multi-language Support
- French, English, Arabic
- RTL support for Arabic
- Localized content management

## 💼 Business Model
- Freemium model with premium merchant subscriptions
- Google Ads integration
- Sponsored promotion features
- Analytics and reporting services

## 🔒 Security & Performance
- JWT-based authentication
- API rate limiting
- Data encryption at rest and in transit
- Optimized database queries with indexing
- Redis caching for improved performance

## 🛠️ Development Setup

### Backend Setup (Working ✅)
```bash
cd backend
npm install
cp .env.example .env
# Edit .env with your configuration
npm start
# Server runs on http://localhost:5000
```

### Merchant Portal Setup (Issues ⚠️)
```bash
cd merchant-portal
npm install
cp .env.example .env.local
# Edit .env.local with your configuration
npm run dev
# Note: Currently has SWC compilation issues
```

### Docker Deployment
```bash
cd deployment
cp .env.example .env
# Edit .env with your configuration
docker-compose up -d
```

## 📊 API Endpoints (Working)

### Health Check
- `GET /health` - Server health status

### Categories
- `GET /api/categories` - List all categories

### Promotions
- `GET /api/promotions` - List promotions
- `POST /api/promotions` - Create promotion (auth required)

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration

## 🐛 Known Issues

### Merchant Portal
- **SWC Binary Error**: Next.js fails to load SWC binary for Windows
- **Workaround**: Install correct SWC binary or configure Babel fallback

### Docker Desktop
- **Startup Issues**: Docker daemon connection errors on some systems
- **Workaround**: Restart Docker Desktop as administrator

## 🚀 Deployment

### Production Deployment
1. Configure environment variables in `deployment/.env`
2. Run `docker-compose up -d` in deployment directory
3. Access via configured domain or localhost

### Development Deployment
1. Start backend: `cd backend && npm start`
2. Start frontend: `cd merchant-portal && npm run dev`
3. Access backend at http://localhost:5000
4. Access frontend at http://localhost:3000

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License
Proprietary - All rights reserved

## 📞 Support
For technical support or questions, please contact the development team.
