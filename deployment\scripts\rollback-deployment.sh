#!/bin/bash

# PromoTun Deployment Rollback Script
# Rolls back to previous stable deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ROLLBACK_VERSION=${1:-latest-stable}
APP_DIR="/opt/promotun"
BACKUP_DIR="/opt/promotun/backups"
COMPOSE_FILE="docker-compose.production.yml"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running as promotun user
check_user() {
    if [[ $(whoami) != "promotun" ]]; then
        print_error "This script must be run as the promotun user"
        exit 1
    fi
}

# List available backups
list_backups() {
    print_info "Available backups:"
    echo
    
    if [[ -d "$BACKUP_DIR" ]]; then
        ls -la "$BACKUP_DIR"/*.tar.gz 2>/dev/null | while read -r line; do
            echo "  $line"
        done
    else
        print_warning "No backup directory found"
    fi
    
    echo
}

# Confirm rollback
confirm_rollback() {
    print_warning "This will rollback the PromoTun deployment to: $ROLLBACK_VERSION"
    print_warning "Current deployment will be stopped and replaced."
    echo
    
    read -p "Are you sure you want to proceed? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Rollback cancelled"
        exit 0
    fi
}

# Create emergency backup
create_emergency_backup() {
    print_info "Creating emergency backup before rollback..."
    
    ./create-backup.sh "emergency-before-rollback-$(date +%Y%m%d_%H%M%S)"
    
    print_status "Emergency backup created"
}

# Stop current deployment
stop_current_deployment() {
    print_info "Stopping current deployment..."
    
    docker-compose -f $COMPOSE_FILE down
    
    print_status "Current deployment stopped"
}

# Rollback to Docker images
rollback_docker_images() {
    print_info "Rolling back Docker images..."
    
    if [[ "$ROLLBACK_VERSION" == "latest-stable" ]]; then
        # Use latest stable images from registry
        if [[ -n "$CI_REGISTRY_PASSWORD" ]]; then
            echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
            
            docker pull $CI_REGISTRY_IMAGE/backend:stable
            docker pull $CI_REGISTRY_IMAGE/merchant-portal:stable
            docker pull $CI_REGISTRY_IMAGE/admin-dashboard:stable
            
            # Tag as latest for compose
            docker tag $CI_REGISTRY_IMAGE/backend:stable $CI_REGISTRY_IMAGE/backend:latest
            docker tag $CI_REGISTRY_IMAGE/merchant-portal:stable $CI_REGISTRY_IMAGE/merchant-portal:latest
            docker tag $CI_REGISTRY_IMAGE/admin-dashboard:stable $CI_REGISTRY_IMAGE/admin-dashboard:latest
        else
            print_warning "No registry credentials, using local images"
        fi
    else
        # Use specific version
        if [[ -n "$CI_REGISTRY_PASSWORD" ]]; then
            echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
            
            docker pull $CI_REGISTRY_IMAGE/backend:$ROLLBACK_VERSION
            docker pull $CI_REGISTRY_IMAGE/merchant-portal:$ROLLBACK_VERSION
            docker pull $CI_REGISTRY_IMAGE/admin-dashboard:$ROLLBACK_VERSION
            
            # Tag as latest for compose
            docker tag $CI_REGISTRY_IMAGE/backend:$ROLLBACK_VERSION $CI_REGISTRY_IMAGE/backend:latest
            docker tag $CI_REGISTRY_IMAGE/merchant-portal:$ROLLBACK_VERSION $CI_REGISTRY_IMAGE/merchant-portal:latest
            docker tag $CI_REGISTRY_IMAGE/admin-dashboard:$ROLLBACK_VERSION $CI_REGISTRY_IMAGE/admin-dashboard:latest
        fi
    fi
    
    print_status "Docker images rolled back"
}

# Restore from backup if specified
restore_from_backup() {
    if [[ "$ROLLBACK_VERSION" == backup-* ]]; then
        local backup_name=${ROLLBACK_VERSION#backup-}
        local backup_file="$BACKUP_DIR/${backup_name}.tar.gz"
        
        if [[ -f "$backup_file" ]]; then
            print_info "Restoring from backup: $backup_name"
            
            # Extract backup
            cd $BACKUP_DIR
            tar xzf "${backup_name}.tar.gz"
            
            # Restore configuration files
            if [[ -f "$BACKUP_DIR/$backup_name/deployment_configs.tar.gz" ]]; then
                cd $APP_DIR
                tar xzf "$BACKUP_DIR/$backup_name/deployment_configs.tar.gz"
            fi
            
            # Restore SSL certificates
            if [[ -f "$BACKUP_DIR/$backup_name/ssl_certificates.tar.gz" ]]; then
                cd $APP_DIR
                tar xzf "$BACKUP_DIR/$backup_name/ssl_certificates.tar.gz"
            fi
            
            # Restore Docker volumes
            if [[ -f "$BACKUP_DIR/$backup_name/backend_uploads.tar.gz" ]]; then
                docker run --rm \
                    -v promotun_backend_uploads:/data \
                    -v $BACKUP_DIR/$backup_name:/backup \
                    alpine tar xzf /backup/backend_uploads.tar.gz -C /data
            fi
            
            if [[ -f "$BACKUP_DIR/$backup_name/grafana_data.tar.gz" ]]; then
                docker run --rm \
                    -v promotun_grafana_data:/data \
                    -v $BACKUP_DIR/$backup_name:/backup \
                    alpine tar xzf /backup/grafana_data.tar.gz -C /data
            fi
            
            # Clean up extracted backup
            rm -rf "$BACKUP_DIR/$backup_name"
            
            print_status "Backup restored"
        else
            print_error "Backup file not found: $backup_file"
            exit 1
        fi
    fi
}

# Start rollback deployment
start_rollback_deployment() {
    print_info "Starting rollback deployment..."
    
    # Start services
    docker-compose -f $COMPOSE_FILE up -d
    
    if [[ $? -eq 0 ]]; then
        print_status "Rollback deployment started"
    else
        print_error "Failed to start rollback deployment"
        exit 1
    fi
}

# Wait for services
wait_for_services() {
    print_info "Waiting for services to be ready..."
    
    # Wait for backend health check
    for i in {1..30}; do
        if curl -f -s http://localhost:5000/health > /dev/null; then
            print_status "Backend service is ready"
            break
        fi
        
        if [[ $i -eq 30 ]]; then
            print_error "Backend service failed to start"
            exit 1
        fi
        
        sleep 10
    done
    
    # Wait for frontend services
    sleep 30
    
    print_status "All services are ready"
}

# Verify rollback
verify_rollback() {
    print_info "Verifying rollback deployment..."
    
    # Run post-deployment tests
    if [[ -f "./post-deployment-tests.sh" ]]; then
        ./post-deployment-tests.sh
    else
        # Basic verification
        if curl -f -s https://promodetect.com/health > /dev/null; then
            print_status "Basic health check passed"
        else
            print_error "Basic health check failed"
            exit 1
        fi
    fi
    
    print_status "Rollback verification completed"
}

# Show rollback summary
show_summary() {
    print_info "Rollback Summary"
    echo "================"
    echo
    
    echo "🔄 Rollback Details:"
    echo "   Version: $ROLLBACK_VERSION"
    echo "   Completed: $(date)"
    echo "   Status: Success"
    echo
    
    echo "🌐 Service URLs:"
    echo "   Main Site: https://promodetect.com"
    echo "   API: https://promodetect.com/api"
    echo "   Admin: https://promodetect.com/admin"
    echo
    
    echo "📊 Container Status:"
    docker-compose -f $COMPOSE_FILE ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"
    echo
    
    echo "📋 Next Steps:"
    echo "   1. Monitor application logs"
    echo "   2. Verify all functionality"
    echo "   3. Investigate original deployment issue"
    echo "   4. Plan fix and re-deployment"
    echo
}

# Main execution
main() {
    print_info "Starting PromoTun deployment rollback..."
    print_info "Rollback version: $ROLLBACK_VERSION"
    echo
    
    check_user
    list_backups
    confirm_rollback
    create_emergency_backup
    stop_current_deployment
    rollback_docker_images
    restore_from_backup
    start_rollback_deployment
    wait_for_services
    verify_rollback
    show_summary
    
    print_status "Deployment rollback completed successfully! 🎉"
    echo
    print_info "Application has been rolled back to: $ROLLBACK_VERSION"
}

# Handle script arguments
case "${1:-rollback}" in
    "list")
        list_backups
        ;;
    "rollback"|*)
        main "$@"
        ;;
esac
