{"name": "PromoTunMobile", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx"}, "dependencies": {"expo": "~49.0.0", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.3", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/drawer": "^6.6.3", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "expo-location": "~16.1.0", "expo-notifications": "~0.20.1", "expo-constants": "~14.4.2", "expo-device": "~5.4.0", "expo-image-picker": "~14.3.2", "expo-secure-store": "~12.3.1", "expo-font": "~11.4.0", "expo-splash-screen": "~0.20.5", "expo-localization": "~14.3.0", "react-native-vector-icons": "^10.0.0", "react-native-elements": "^3.4.3", "react-native-paper": "^5.9.1", "react-native-maps": "1.7.1", "react-native-svg": "13.9.0", "axios": "^1.4.0", "react-query": "^3.39.3", "zustand": "^4.4.1", "react-hook-form": "^7.45.2", "yup": "^1.2.0", "@hookform/resolvers": "^3.1.1", "react-native-flash-message": "^0.4.2", "react-native-modal": "^13.0.1", "react-native-skeleton-placeholder": "^5.2.4", "react-native-super-grid": "^4.4.4", "react-native-image-viewing": "^0.2.2", "react-native-share": "^9.4.1", "react-native-rate": "^1.2.12", "i18next": "^23.2.11", "react-i18next": "^13.0.2", "i18next-browser-languagedetector": "^7.1.0", "react-native-localize": "^3.0.2", "socket.io-client": "^4.7.2", "react-native-push-notification": "^8.1.1", "@react-native-async-storage/async-storage": "1.18.2", "react-native-keychain": "^8.1.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "typescript": "^5.1.3", "jest": "^29.2.1", "jest-expo": "~49.0.0", "react-test-renderer": "18.2.0", "@testing-library/react-native": "^12.1.2", "@testing-library/jest-native": "^5.4.2", "eslint": "^8.44.0", "eslint-config-expo": "^7.0.0", "prettier": "^3.0.0"}, "jest": {"preset": "jest-expo"}, "private": true}