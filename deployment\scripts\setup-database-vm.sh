#!/bin/bash

# PromoTun Database VM Setup Script
# Ubuntu 22.04 LTS - Database Server (**************)
# This script sets up PostgreSQL, security hardening, and database configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DB_VM_IP="**************"
APP_VM_IP="**************"
DB_NAME="promodetect"
DB_USER="postgres"
BACKUP_DIR="/var/backups/postgresql"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        print_error "This script should not be run as root. Please run as a regular user with sudo privileges."
        exit 1
    fi
}

# Update system packages
update_system() {
    print_info "Updating system packages..."
    sudo apt update && sudo apt upgrade -y
    sudo apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release
    print_status "System packages updated"
}

# Install PostgreSQL
install_postgresql() {
    print_info "Installing PostgreSQL 15..."
    
    # Add PostgreSQL official repository
    wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo apt-key add -
    echo "deb http://apt.postgresql.org/pub/repos/apt/ $(lsb_release -cs)-pgdg main" | sudo tee /etc/apt/sources.list.d/pgdg.list
    
    # Update and install PostgreSQL
    sudo apt update
    sudo apt install -y postgresql-15 postgresql-client-15 postgresql-contrib-15
    
    # Start and enable PostgreSQL
    sudo systemctl start postgresql
    sudo systemctl enable postgresql
    
    print_status "PostgreSQL installed successfully"
}

# Configure PostgreSQL
configure_postgresql() {
    print_info "Configuring PostgreSQL..."
    
    # Generate secure password
    DB_PASSWORD=$(openssl rand -base64 32)
    
    # Configure PostgreSQL
    sudo -u postgres psql <<EOF
-- Create database
CREATE DATABASE ${DB_NAME};

-- Set password for postgres user
ALTER USER postgres PASSWORD '${DB_PASSWORD}';

-- Create application user (optional)
CREATE USER promotun_app WITH PASSWORD '${DB_PASSWORD}';
GRANT ALL PRIVILEGES ON DATABASE ${DB_NAME} TO promotun_app;

-- Exit
\q
EOF
    
    # Save password to secure file
    echo "Database Password: ${DB_PASSWORD}" | sudo tee /root/db_credentials.txt
    sudo chmod 600 /root/db_credentials.txt
    
    print_status "PostgreSQL configured"
    print_warning "Database password saved to /root/db_credentials.txt"
    print_warning "Please update your .env.production file with this password"
}

# Configure PostgreSQL for remote connections
configure_remote_access() {
    print_info "Configuring PostgreSQL for remote access..."
    
    # Backup original configuration
    sudo cp /etc/postgresql/15/main/postgresql.conf /etc/postgresql/15/main/postgresql.conf.backup
    sudo cp /etc/postgresql/15/main/pg_hba.conf /etc/postgresql/15/main/pg_hba.conf.backup
    
    # Configure postgresql.conf
    sudo tee -a /etc/postgresql/15/main/postgresql.conf > /dev/null <<EOF

# PromoTun Configuration
listen_addresses = '${DB_VM_IP},localhost'
port = 5432
max_connections = 100
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100

# Logging
log_destination = 'stderr'
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_duration_statement = 1000
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on

# Security
ssl = on
ssl_cert_file = '/etc/ssl/certs/ssl-cert-snakeoil.pem'
ssl_key_file = '/etc/ssl/private/ssl-cert-snakeoil.key'
EOF
    
    # Configure pg_hba.conf for application VM access
    sudo tee -a /etc/postgresql/15/main/pg_hba.conf > /dev/null <<EOF

# PromoTun Application Access
host    ${DB_NAME}    postgres    ${APP_VM_IP}/32    md5
host    ${DB_NAME}    promotun_app    ${APP_VM_IP}/32    md5

# SSL connections
hostssl    ${DB_NAME}    postgres    ${APP_VM_IP}/32    md5
hostssl    ${DB_NAME}    promotun_app    ${APP_VM_IP}/32    md5
EOF
    
    # Restart PostgreSQL
    sudo systemctl restart postgresql
    
    print_status "PostgreSQL remote access configured"
}

# Configure firewall
configure_firewall() {
    print_info "Configuring UFW firewall..."
    
    # Enable UFW
    sudo ufw --force enable
    
    # Default policies
    sudo ufw default deny incoming
    sudo ufw default allow outgoing
    
    # SSH access
    sudo ufw allow ssh
    sudo ufw allow 22/tcp
    
    # PostgreSQL access only from application VM
    sudo ufw allow from ${APP_VM_IP} to any port 5432
    
    # Monitoring access from application VM
    sudo ufw allow from ${APP_VM_IP} to any port 9100
    
    print_status "Firewall configured"
}

# Security hardening
security_hardening() {
    print_info "Applying security hardening..."
    
    # Install fail2ban
    sudo apt install -y fail2ban
    
    # Configure fail2ban
    sudo tee /etc/fail2ban/jail.local > /dev/null <<EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3
bantime = 3600
EOF
    
    # Start fail2ban
    sudo systemctl enable fail2ban
    sudo systemctl start fail2ban
    
    # Configure automatic security updates
    sudo apt install -y unattended-upgrades
    sudo dpkg-reconfigure -plow unattended-upgrades
    
    print_status "Security hardening applied"
}

# Setup database backup
setup_backup() {
    print_info "Setting up database backup system..."
    
    # Create backup directory
    sudo mkdir -p ${BACKUP_DIR}
    sudo chown postgres:postgres ${BACKUP_DIR}
    
    # Create backup script
    sudo tee /usr/local/bin/backup-promotun-db.sh > /dev/null <<EOF
#!/bin/bash

# PromoTun Database Backup Script
BACKUP_DIR="${BACKUP_DIR}"
DATE=\$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

# Create backup
sudo -u postgres pg_dump ${DB_NAME} | gzip > \${BACKUP_DIR}/promotun_\${DATE}.sql.gz

# Create globals backup
sudo -u postgres pg_dumpall --globals-only | gzip > \${BACKUP_DIR}/globals_\${DATE}.sql.gz

# Clean old backups
find \${BACKUP_DIR} -name "*.sql.gz" -mtime +\${RETENTION_DAYS} -delete

echo "Database backup completed: \${DATE}"
EOF
    
    sudo chmod +x /usr/local/bin/backup-promotun-db.sh
    
    # Setup cron job for daily backups
    echo "0 1 * * * /usr/local/bin/backup-promotun-db.sh >> /var/log/promotun-backup.log 2>&1" | sudo crontab -
    
    print_status "Database backup system configured"
}

# Install monitoring
install_monitoring() {
    print_info "Installing monitoring tools..."
    
    # Install htop and other monitoring tools
    sudo apt install -y htop iotop nethogs ncdu tree
    
    # Install node_exporter for Prometheus monitoring
    NODE_EXPORTER_VERSION="1.6.1"
    wget https://github.com/prometheus/node_exporter/releases/download/v${NODE_EXPORTER_VERSION}/node_exporter-${NODE_EXPORTER_VERSION}.linux-amd64.tar.gz
    tar xvfz node_exporter-${NODE_EXPORTER_VERSION}.linux-amd64.tar.gz
    sudo mv node_exporter-${NODE_EXPORTER_VERSION}.linux-amd64/node_exporter /usr/local/bin/
    rm -rf node_exporter-${NODE_EXPORTER_VERSION}*
    
    # Create node_exporter service
    sudo tee /etc/systemd/system/node_exporter.service > /dev/null <<EOF
[Unit]
Description=Node Exporter
Wants=network-online.target
After=network-online.target

[Service]
User=nobody
Group=nobody
Type=simple
ExecStart=/usr/local/bin/node_exporter --web.listen-address=${DB_VM_IP}:9100

[Install]
WantedBy=multi-user.target
EOF
    
    sudo systemctl daemon-reload
    sudo systemctl enable node_exporter
    sudo systemctl start node_exporter
    
    print_status "Monitoring tools installed"
}

# Create database schema
create_schema() {
    print_info "Creating database schema..."
    
    # Create schema file
    sudo -u postgres tee /tmp/schema.sql > /dev/null <<'EOF'
-- PromoTun Database Schema

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    phone VARCHAR(20),
    role VARCHAR(20) DEFAULT 'consumer' CHECK (role IN ('consumer', 'merchant', 'admin')),
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    name_fr VARCHAR(100),
    name_ar VARCHAR(100),
    description TEXT,
    icon VARCHAR(100),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Merchants table
CREATE TABLE IF NOT EXISTS merchants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    business_name VARCHAR(255) NOT NULL,
    description TEXT,
    address TEXT,
    city VARCHAR(100),
    country VARCHAR(100),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    phone VARCHAR(20),
    website VARCHAR(255),
    is_verified BOOLEAN DEFAULT false,
    subscription_type VARCHAR(20) DEFAULT 'free' CHECK (subscription_type IN ('free', 'premium', 'enterprise')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Promotions table
CREATE TABLE IF NOT EXISTS promotions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    merchant_id UUID REFERENCES merchants(id) ON DELETE CASCADE,
    category_id UUID REFERENCES categories(id),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    original_price DECIMAL(10, 2),
    discounted_price DECIMAL(10, 2),
    discount_percentage INTEGER,
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    view_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_promotions_merchant ON promotions(merchant_id);
CREATE INDEX IF NOT EXISTS idx_promotions_category ON promotions(category_id);
CREATE INDEX IF NOT EXISTS idx_promotions_active ON promotions(is_active);
CREATE INDEX IF NOT EXISTS idx_promotions_dates ON promotions(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_merchants_location ON merchants(latitude, longitude);

-- Insert sample categories
INSERT INTO categories (name, name_fr, name_ar, description, sort_order) VALUES
('Electronics', 'Électronique', 'إلكترونيات', 'Electronic devices and gadgets', 1),
('Fashion', 'Mode', 'أزياء', 'Clothing and accessories', 2),
('Food & Dining', 'Restauration', 'طعام ومطاعم', 'Restaurants and food services', 3),
('Health & Beauty', 'Santé et Beauté', 'صحة وجمال', 'Health and beauty products', 4),
('Home & Garden', 'Maison et Jardin', 'منزل وحديقة', 'Home improvement and garden supplies', 5)
ON CONFLICT DO NOTHING;
EOF
    
    # Apply schema
    sudo -u postgres psql -d ${DB_NAME} -f /tmp/schema.sql
    sudo rm /tmp/schema.sql
    
    print_status "Database schema created"
}

# Main execution
main() {
    print_info "Starting PromoTun Database VM setup..."
    print_info "Database VM IP: ${DB_VM_IP}"
    print_info "Application VM IP: ${APP_VM_IP}"
    print_info "Database Name: ${DB_NAME}"
    echo
    
    check_root
    update_system
    install_postgresql
    configure_postgresql
    configure_remote_access
    configure_firewall
    security_hardening
    setup_backup
    install_monitoring
    create_schema
    
    print_status "Database VM setup completed successfully!"
    echo
    print_info "Database connection details:"
    echo "Host: ${DB_VM_IP}"
    echo "Port: 5432"
    echo "Database: ${DB_NAME}"
    echo "Username: postgres"
    echo "Password: Check /root/db_credentials.txt"
    echo
    print_info "Next steps:"
    echo "1. Copy the database password from /root/db_credentials.txt"
    echo "2. Update .env.production on the application VM"
    echo "3. Test database connection from application VM"
    echo "4. Configure SSL certificates (optional but recommended)"
}

# Run main function
main "$@"
