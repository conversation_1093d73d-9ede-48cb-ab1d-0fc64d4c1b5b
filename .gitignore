# PromoTun - Comprehensive .gitignore

# ===================================
# Node.js Dependencies
# ===================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# ===================================
# Build Outputs & Artifacts
# ===================================
# Next.js
.next/
out/
build/
dist/

# React Native
android/app/build/
ios/build/
*.ipa
*.apk

# General build artifacts
*.tgz
*.tar.gz

# ===================================
# Environment & Configuration Files
# ===================================
# Environment files with sensitive data
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Keep template files
!.env.example
!.env.template
!.env.production.template
!deployment/.env.production.template

# ===================================
# Logs & Temporary Files
# ===================================
logs/
*.log
log/
temp/
tmp/
.tmp/

# ===================================
# Database & Cache
# ===================================
# Database files
*.db
*.sqlite
*.sqlite3

# Redis dumps
dump.rdb

# ===================================
# IDE & Editor Files
# ===================================
# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# IntelliJ IDEA
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# ===================================
# OS Generated Files
# ===================================
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~
.fuse_hidden*
.directory
.Trash-*

# ===================================
# Docker & Deployment
# ===================================
# Docker volumes and data
docker-data/
postgres_data/
redis_data/
backend_uploads/
backend_logs/

# ===================================
# Testing & Coverage
# ===================================
coverage/
.nyc_output/
.coverage
*.lcov

# Jest
jest_coverage/

# ===================================
# Package Manager Files
# ===================================
# npm
package-lock.json
npm-shrinkwrap.json

# Yarn
yarn.lock
.yarn/
.pnp.*

# pnpm
pnpm-lock.yaml

# ===================================
# Mobile Development
# ===================================
# React Native
.expo/
.expo-shared/

# iOS
ios/Pods/
ios/build/
ios/DerivedData/
*.xcworkspace
*.xcuserdata

# Android
android/.gradle/
android/app/build/
android/build/
android/local.properties
android/keystore.properties

# ===================================
# Security & Certificates
# ===================================
# SSL certificates
*.pem
*.key
*.crt
*.csr
*.p12

# API keys and secrets
secrets/
credentials/

# ===================================
# Monitoring & Analytics
# ===================================
# Grafana
grafana_data/

# Prometheus
prometheus_data/

# ===================================
# Uploads & User Content
# ===================================
uploads/
user-uploads/
static/uploads/

# ===================================
# Backup Files
# ===================================
*.backup
*.bak
*.old
*.orig

# ===================================
# Runtime & Process Files
# ===================================
*.pid
*.seed
*.pid.lock

# ===================================
# TypeScript
# ===================================
*.tsbuildinfo
.tscache/

# ===================================
# ESLint & Prettier
# ===================================
.eslintcache

# ===================================
# Miscellaneous
# ===================================
# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
.tmp/
.temp/

# ===================================
# Project Specific Exclusions
# ===================================
# Deployment scripts with sensitive data
deploy-production.js
deploy-staging.js

# Local development utilities
local-*.js
dev-*.js

# Test data
test-data/
mock-data/

# Documentation builds
docs/build/
docs/.vuepress/dist/
