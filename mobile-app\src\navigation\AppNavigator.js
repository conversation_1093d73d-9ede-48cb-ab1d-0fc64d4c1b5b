import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { useAuthStore } from '../store/authStore';
import { colors } from '../constants/theme';

// Auth Screens
import LoginScreen from '../screens/auth/LoginScreen';
import RegisterScreen from '../screens/auth/RegisterScreen';
import ForgotPasswordScreen from '../screens/auth/ForgotPasswordScreen';
import VerificationScreen from '../screens/auth/VerificationScreen';

// Main App Screens
import HomeScreen from '../screens/main/HomeScreen';
import SearchScreen from '../screens/main/SearchScreen';
import FavoritesScreen from '../screens/main/FavoritesScreen';
import ProfileScreen from '../screens/main/ProfileScreen';
import NotificationsScreen from '../screens/main/NotificationsScreen';

// Promotion Screens
import PromotionDetailsScreen from '../screens/promotions/PromotionDetailsScreen';
import CategoryScreen from '../screens/promotions/CategoryScreen';
import MerchantScreen from '../screens/promotions/MerchantScreen';

// Settings Screens
import SettingsScreen from '../screens/settings/SettingsScreen';
import LanguageScreen from '../screens/settings/LanguageScreen';
import LocationSettingsScreen from '../screens/settings/LocationSettingsScreen';
import NotificationSettingsScreen from '../screens/settings/NotificationSettingsScreen';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();
const Drawer = createDrawerNavigator();

// Auth Stack Navigator
function AuthNavigator() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: colors.background }
      }}
    >
      <Stack.Screen name="Login" component={LoginScreen} />
      <Stack.Screen name="Register" component={RegisterScreen} />
      <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
      <Stack.Screen name="Verification" component={VerificationScreen} />
    </Stack.Navigator>
  );
}

// Home Stack Navigator
function HomeStackNavigator() {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="HomeMain" 
        component={HomeScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen 
        name="PromotionDetails" 
        component={PromotionDetailsScreen}
        options={{ title: 'Promotion Details' }}
      />
      <Stack.Screen 
        name="Category" 
        component={CategoryScreen}
        options={({ route }) => ({ title: route.params?.categoryName || 'Category' })}
      />
      <Stack.Screen 
        name="Merchant" 
        component={MerchantScreen}
        options={({ route }) => ({ title: route.params?.merchantName || 'Merchant' })}
      />
    </Stack.Navigator>
  );
}

// Search Stack Navigator
function SearchStackNavigator() {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="SearchMain" 
        component={SearchScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen 
        name="PromotionDetails" 
        component={PromotionDetailsScreen}
        options={{ title: 'Promotion Details' }}
      />
    </Stack.Navigator>
  );
}

// Favorites Stack Navigator
function FavoritesStackNavigator() {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="FavoritesMain" 
        component={FavoritesScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen 
        name="PromotionDetails" 
        component={PromotionDetailsScreen}
        options={{ title: 'Promotion Details' }}
      />
    </Stack.Navigator>
  );
}

// Profile Stack Navigator
function ProfileStackNavigator() {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="ProfileMain" 
        component={ProfileScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen 
        name="Settings" 
        component={SettingsScreen}
        options={{ title: 'Settings' }}
      />
      <Stack.Screen 
        name="Language" 
        component={LanguageScreen}
        options={{ title: 'Language' }}
      />
      <Stack.Screen 
        name="LocationSettings" 
        component={LocationSettingsScreen}
        options={{ title: 'Location Settings' }}
      />
      <Stack.Screen 
        name="NotificationSettings" 
        component={NotificationSettingsScreen}
        options={{ title: 'Notification Settings' }}
      />
      <Stack.Screen 
        name="Notifications" 
        component={NotificationsScreen}
        options={{ title: 'Notifications' }}
      />
    </Stack.Navigator>
  );
}

// Main Tab Navigator
function MainTabNavigator() {
  const { t } = useTranslation();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          switch (route.name) {
            case 'Home':
              iconName = 'home';
              break;
            case 'Search':
              iconName = 'search';
              break;
            case 'Favorites':
              iconName = 'favorite';
              break;
            case 'Profile':
              iconName = 'person';
              break;
            default:
              iconName = 'circle';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.textSecondary,
        tabBarStyle: {
          backgroundColor: colors.surface,
          borderTopColor: colors.border,
        },
        headerShown: false,
      })}
    >
      <Tab.Screen 
        name="Home" 
        component={HomeStackNavigator}
        options={{ tabBarLabel: t('navigation.home') }}
      />
      <Tab.Screen 
        name="Search" 
        component={SearchStackNavigator}
        options={{ tabBarLabel: t('navigation.search') }}
      />
      <Tab.Screen 
        name="Favorites" 
        component={FavoritesStackNavigator}
        options={{ tabBarLabel: t('navigation.favorites') }}
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileStackNavigator}
        options={{ tabBarLabel: t('navigation.profile') }}
      />
    </Tab.Navigator>
  );
}

// Main App Navigator
export default function AppNavigator() {
  const { user, isLoading } = useAuthStore();

  if (isLoading) {
    // You can return a loading screen here
    return null;
  }

  return user ? <MainTabNavigator /> : <AuthNavigator />;
}
