const request = require('supertest');
const app = require('../server');
const { query } = require('../database/connection');

describe('API Endpoints Tests', () => {
  let authToken;
  let merchantToken;
  let testUserId;
  let testMerchantId;
  let testPromotionId;

  beforeAll(async () => {
    // Create test consumer
    const consumerResponse = await request(app)
      .post('/api/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'TestPass123!',
        firstName: 'Test',
        lastName: 'Consumer',
        userType: 'consumer',
        preferredLanguage: 'en'
      });

    authToken = consumerResponse.body.data.token;
    testUserId = consumerResponse.body.data.user.id;

    // Create test merchant
    const merchantResponse = await request(app)
      .post('/api/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'TestPass123!',
        firstName: 'Test',
        lastName: 'Merchant',
        userType: 'merchant',
        preferredLanguage: 'en'
      });

    merchantToken = merchantResponse.body.data.token;
    testMerchantId = merchantResponse.body.data.user.id;
  });

  afterAll(async () => {
    // Cleanup test data
    if (testPromotionId) {
      await query('DELETE FROM promotions WHERE id = $1', [testPromotionId]);
    }
    if (testUserId) {
      await query('DELETE FROM users WHERE id = $1', [testUserId]);
    }
    if (testMerchantId) {
      await query('DELETE FROM users WHERE id = $1', [testMerchantId]);
    }
  });

  describe('Authentication Endpoints', () => {
    test('POST /api/auth/register - should register new user', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'NewPass123!',
        firstName: 'New',
        lastName: 'User',
        userType: 'consumer',
        preferredLanguage: 'en'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe(userData.email);
      expect(response.body.data.token).toBeDefined();

      // Cleanup
      await query('DELETE FROM users WHERE email = $1', [userData.email]);
    });

    test('POST /api/auth/login - should login existing user', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'TestPass123!'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe(loginData.email);
      expect(response.body.data.token).toBeDefined();
    });

    test('POST /api/auth/login - should reject invalid credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData);

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });

    test('POST /api/auth/logout - should logout user', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });
  });

  describe('User Profile Endpoints', () => {
    test('GET /api/users/profile - should get user profile', async () => {
      const response = await request(app)
        .get('/api/users/profile')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe('<EMAIL>');
    });

    test('PUT /api/users/profile - should update user profile', async () => {
      const updateData = {
        firstName: 'Updated',
        lastName: 'Name',
        phone: '+1234567890'
      };

      const response = await request(app)
        .put('/api/users/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.user.firstName).toBe(updateData.firstName);
    });
  });

  describe('Promotions Endpoints', () => {
    test('GET /api/promotions - should get promotions list', async () => {
      const response = await request(app)
        .get('/api/promotions')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.promotions).toBeDefined();
      expect(response.body.data.pagination).toBeDefined();
    });

    test('GET /api/promotions with filters - should filter promotions', async () => {
      const response = await request(app)
        .get('/api/promotions?page=1&limit=10&search=electronics')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.pagination.page).toBe(1);
      expect(response.body.data.pagination.limit).toBe(10);
    });

    test('POST /api/promotions - should create promotion (merchant only)', async () => {
      // First get a category ID
      const categoriesResponse = await request(app)
        .get('/api/categories');
      
      const categoryId = categoriesResponse.body.data.categories[0]?.id;

      const promotionData = {
        title: 'Test Promotion',
        description: 'Test promotion description',
        categoryId: categoryId,
        originalPrice: 100.00,
        discountedPrice: 50.00,
        promotionType: 'percentage',
        startDate: new Date().toISOString(),
        endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        termsConditions: 'Test terms and conditions'
      };

      const response = await request(app)
        .post('/api/promotions')
        .set('Authorization', `Bearer ${merchantToken}`)
        .send(promotionData);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.promotion.title).toBe(promotionData.title);
      
      testPromotionId = response.body.data.promotion.id;
    });

    test('GET /api/promotions/:id - should get promotion details', async () => {
      if (!testPromotionId) {
        return; // Skip if no promotion was created
      }

      const response = await request(app)
        .get(`/api/promotions/${testPromotionId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.promotion.id).toBe(testPromotionId);
    });
  });

  describe('Categories Endpoints', () => {
    test('GET /api/categories - should get categories list', async () => {
      const response = await request(app)
        .get('/api/categories');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.categories).toBeDefined();
      expect(Array.isArray(response.body.data.categories)).toBe(true);
    });
  });

  describe('Favorites Endpoints', () => {
    test('GET /api/users/favorites - should get user favorites', async () => {
      const response = await request(app)
        .get('/api/users/favorites')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.favorites).toBeDefined();
    });

    test('POST /api/users/favorites - should add to favorites', async () => {
      if (!testPromotionId) {
        return; // Skip if no promotion was created
      }

      const response = await request(app)
        .post('/api/users/favorites')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ promotionId: testPromotionId });

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
    });

    test('DELETE /api/users/favorites/:promotionId - should remove from favorites', async () => {
      if (!testPromotionId) {
        return; // Skip if no promotion was created
      }

      const response = await request(app)
        .delete(`/api/users/favorites/${testPromotionId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });
  });

  describe('Error Handling', () => {
    test('should return 404 for non-existent endpoints', async () => {
      const response = await request(app)
        .get('/api/nonexistent');

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });

    test('should return 400 for invalid JSON', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .set('Content-Type', 'application/json')
        .send('invalid json');

      expect(response.status).toBe(400);
    });

    test('should handle database errors gracefully', async () => {
      // This would test database error scenarios
      // For now, we'll test with invalid data that would cause DB constraints
      const invalidData = {
        email: '<EMAIL>', // Duplicate email
        password: 'TestPass123!',
        firstName: 'Test',
        lastName: 'User',
        userType: 'consumer'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(invalidData);

      expect(response.status).toBe(409);
      expect(response.body.success).toBe(false);
    });
  });

  describe('Response Format Validation', () => {
    test('should return consistent response format for success', async () => {
      const response = await request(app)
        .get('/api/categories');

      expect(response.body).toHaveProperty('success');
      expect(response.body).toHaveProperty('data');
      expect(response.body.success).toBe(true);
    });

    test('should return consistent response format for errors', async () => {
      const response = await request(app)
        .get('/api/users/profile'); // No auth token

      expect(response.body).toHaveProperty('success');
      expect(response.body).toHaveProperty('message');
      expect(response.body.success).toBe(false);
    });
  });
});
