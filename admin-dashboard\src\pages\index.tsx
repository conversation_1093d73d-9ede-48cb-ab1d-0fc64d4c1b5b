import React from 'react';
import Head from 'next/head';
import { Container, Typography, Box, Card, CardContent, Grid } from '@mui/material';
import { AdminPanelSettings, Analytics, People, Store, Security, Settings } from '@mui/icons-material';

const AdminDashboard: React.FC = () => {
  return (
    <>
      <Head>
        <title>PromoTun Admin Dashboard</title>
        <meta name="description" content="PromoTun Admin Dashboard - System Management" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ mb: 4 }}>
          <Typography variant="h3" component="h1" gutterBottom>
            PromoTun Admin Dashboard
          </Typography>
          <Typography variant="h6" color="text.secondary">
            System administration and management console
          </Typography>
        </Box>

        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={4}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <People color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6">User Management</Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Manage users, merchants, and administrators
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Store color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6">Merchant Approval</Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Review and approve merchant applications
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Analytics color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6">System Analytics</Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  View system-wide analytics and reports
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Security color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6">Security</Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Monitor security events and manage access
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Settings color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6">System Settings</Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Configure system parameters and settings
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <AdminPanelSettings color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6">Content Moderation</Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Review and moderate user-generated content
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Box sx={{ mt: 4, p: 3, bgcolor: 'background.paper', borderRadius: 2 }}>
          <Typography variant="h5" gutterBottom>
            🛡️ Admin Dashboard Active
          </Typography>
          <Typography variant="body1" paragraph>
            The PromoTun Admin Dashboard is running successfully in a Docker container.
            This interface provides comprehensive system administration capabilities.
          </Typography>
          <Typography variant="body2" color="text.secondary">
            API Endpoint: {process.env.NEXT_PUBLIC_API_URL}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Socket URL: {process.env.NEXT_PUBLIC_SOCKET_URL}
          </Typography>
        </Box>
      </Container>
    </>
  );
};

export default AdminDashboard;
