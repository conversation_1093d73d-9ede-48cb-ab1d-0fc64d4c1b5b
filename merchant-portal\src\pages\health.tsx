import React from 'react';
import { NextPage } from 'next';
import { Container, Typography, Box, Card, CardContent } from '@mui/material';
import { CheckCircle } from '@mui/icons-material';

const HealthPage: NextPage = () => {
  return (
    <Container maxWidth="sm" sx={{ mt: 4, mb: 4 }}>
      <Card>
        <CardContent>
          <Box sx={{ textAlign: 'center' }}>
            <CheckCircle color="success" sx={{ fontSize: 60, mb: 2 }} />
            <Typography variant="h4" component="h1" gutterBottom>
              Merchant Portal Health Check
            </Typography>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Status: OK
            </Typography>
            <Typography variant="body1" paragraph>
              The PromoTun Merchant Portal is running successfully in Docker container.
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Timestamp: {new Date().toISOString()}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              API URL: {process.env.NEXT_PUBLIC_API_URL}
            </Typography>
          </Box>
        </CardContent>
      </Card>
    </Container>
  );
};

export default HealthPage;
