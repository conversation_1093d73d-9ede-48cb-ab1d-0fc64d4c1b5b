@echo off
echo.
echo ========================================
echo   PromoTun Docker Deployment Script
echo ========================================
echo.

REM Check if Docker is available
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not installed or not in PATH
    echo.
    echo 💡 Please install Docker Desktop from:
    echo    https://www.docker.com/products/docker-desktop
    echo.
    pause
    exit /b 1
)

echo ✅ Docker is installed
docker --version

REM Check if Docker daemon is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker daemon is not running
    echo.
    echo 💡 Please start Docker Desktop and try again
    echo.
    pause
    exit /b 1
)

echo ✅ Docker daemon is running
echo.

REM Navigate to deployment directory
if not exist "deployment" (
    echo ❌ Deployment directory not found
    echo    Please run this script from the PromoTun root directory
    pause
    exit /b 1
)

cd deployment

echo 🚀 Starting PromoTun Docker deployment...
echo.

REM Stop any existing containers
echo 🧹 Cleaning up existing containers...
docker-compose down -v >nul 2>&1

REM Start the complete stack
echo 🐳 Starting all services...
docker-compose up -d

if %errorlevel% neq 0 (
    echo ❌ Failed to start Docker services
    echo.
    echo 🔧 Troubleshooting tips:
    echo    1. Check if ports 80, 3000, 3001, 5000, 5432, 6379 are available
    echo    2. Ensure Docker has enough memory allocated
    echo    3. Check Docker Desktop settings
    echo.
    pause
    exit /b 1
)

echo.
echo ⏳ Waiting for services to be ready...
timeout /t 30 /nobreak >nul

REM Check service status
echo.
echo 📊 Service Status:
echo ==================
docker-compose ps

echo.
echo 🏥 Running health checks...
echo.

REM Health check backend
echo 🔍 Checking Backend API...
curl -f -s http://localhost:5000/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Backend API: Healthy
) else (
    echo ❌ Backend API: Not responding
)

REM Health check merchant portal
echo 🔍 Checking Merchant Portal...
curl -f -s http://localhost:3000/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Merchant Portal: Healthy
) else (
    echo ❌ Merchant Portal: Not responding
)

REM Health check admin dashboard
echo 🔍 Checking Admin Dashboard...
curl -f -s http://localhost:3001/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Admin Dashboard: Healthy
) else (
    echo ❌ Admin Dashboard: Not responding
)

REM Health check nginx
echo 🔍 Checking Nginx Proxy...
curl -f -s http://localhost/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Nginx Proxy: Healthy
) else (
    echo ❌ Nginx Proxy: Not responding
)

echo.
echo 🎉 PromoTun Docker deployment completed!
echo.
echo 🌐 Service URLs:
echo ================
echo 🔧 Backend API:      http://localhost:5000
echo 🏪 Merchant Portal:  http://localhost:3000
echo 👑 Admin Dashboard:  http://localhost:3001
echo 🌐 Main Application: http://localhost
echo 📊 Grafana:          http://localhost:3002
echo 📈 Prometheus:       http://localhost:9090
echo.
echo 🔑 Test Credentials:
echo ====================
echo 👤 Consumer: <EMAIL> / password
echo 🏪 Merchant: <EMAIL> / password
echo 👑 Admin:    <EMAIL> / password
echo.
echo 🛠️ Management Commands:
echo =======================
echo 📊 Status:   docker-compose ps
echo 📋 Logs:     docker-compose logs -f
echo 🛑 Stop:     docker-compose down
echo 🔄 Restart:  docker-compose restart
echo.
echo 📖 For more information, see:
echo ==============================
echo 📄 DOCKER_DEPLOYMENT_REPORT.md
echo 📄 QUICK_START_GUIDE.md
echo.

REM Ask if user wants to view logs
set /p choice="Would you like to view live logs? (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 📋 Showing live logs (Press Ctrl+C to exit)...
    docker-compose logs -f
)

echo.
echo ✅ PromoTun is now running!
pause
