# PromoTun GitLab CI/CD Pipeline
# Automated testing, building, and deployment for Ubuntu 22.04 VMs

# ===================================
# Global Configuration
# ===================================
image: docker:24.0.5

services:
  - docker:24.0.5-dind

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  DOCKER_HOST: tcp://docker:2376
  DOCKER_TLS_VERIFY: 1
  DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"
  
  # Application Configuration
  APP_VM_HOST: "**************"
  DB_VM_HOST: "**************"
  DOMAIN_NAME: "promodetect.com"
  
  # Docker Registry Configuration
  REGISTRY_URL: "$CI_REGISTRY"
  REGISTRY_USER: "$CI_REGISTRY_USER"
  REGISTRY_PASSWORD: "$CI_REGISTRY_PASSWORD"
  
  # Image Tags
  BACKEND_IMAGE: "$CI_REGISTRY_IMAGE/backend"
  MERCHANT_PORTAL_IMAGE: "$CI_REGISTRY_IMAGE/merchant-portal"
  ADMIN_DASHBOARD_IMAGE: "$CI_REGISTRY_IMAGE/admin-dashboard"
  MOBILE_APP_IMAGE: "$CI_REGISTRY_IMAGE/mobile-app"

# ===================================
# Pipeline Stages
# ===================================
stages:
  - validate
  - test
  - build
  - security-scan
  - deploy-staging
  - deploy-production
  - cleanup

# ===================================
# Stage: Validate
# ===================================
validate-dockerfiles:
  stage: validate
  image: hadolint/hadolint:latest-debian
  script:
    - hadolint backend/Dockerfile
    - hadolint merchant-portal/Dockerfile
    - hadolint admin-dashboard/Dockerfile
    - hadolint mobile-app/Dockerfile
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_COMMIT_BRANCH == "develop"

validate-compose:
  stage: validate
  image: docker/compose:latest
  before_script:
    - cd deployment
    # Create minimal environment file for validation
    - |
      cat > .env.production << EOF
      DOMAIN_NAME=example.com
      DB_HOST=localhost
      DB_PORT=5432
      DB_NAME=promotun_test
      DB_USER=test_user
      DB_PASSWORD=test_password
      REDIS_PASSWORD=test_redis_password
      JWT_SECRET=test_jwt_secret_minimum_32_characters_long
      SESSION_SECRET=test_session_secret_minimum_32_characters_long
      NEXTAUTH_SECRET=test_nextauth_secret_minimum_32_characters_long
      FIREBASE_PROJECT_ID=test-project
      FIREBASE_CLIENT_EMAIL=<EMAIL>
      FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\ntest\n-----END PRIVATE KEY-----"
      SENDGRID_API_KEY=SG.test_sendgrid_api_key
      GOOGLE_MAPS_API_KEY=test_google_maps_api_key
      FROM_EMAIL=<EMAIL>
      FROM_NAME=Test PromoTun
      GRAFANA_PASSWORD=test_grafana_password
      BACKEND_IMAGE=test/backend:latest
      MERCHANT_PORTAL_IMAGE=test/merchant-portal:latest
      ADMIN_DASHBOARD_IMAGE=test/admin-dashboard:latest
      MOBILE_APP_IMAGE=test/mobile-app:latest
      EOF
  script:
    - docker-compose -f docker-compose.production.yml config
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_COMMIT_BRANCH == "develop"

# ===================================
# Stage: Test
# ===================================
test-backend:
  stage: test
  image: node:18-alpine
  services:
    - postgres:15-alpine
    - redis:7-alpine
  variables:
    POSTGRES_DB: promotun_test
    POSTGRES_USER: test_user
    POSTGRES_PASSWORD: test_password
    REDIS_URL: redis://redis:6379
    NODE_ENV: test
  before_script:
    - apk add --no-cache postgresql-client
    - cd backend
    # Verify package-lock.json exists and is valid
    - |
      if [ ! -f package-lock.json ]; then
        echo "ERROR: package-lock.json not found"
        exit 1
      fi
    # Install dependencies
    - npm ci --prefer-offline --no-audit
    # Wait for services to be ready
    - |
      echo "Waiting for PostgreSQL to be ready..."
      until pg_isready -h postgres -p 5432 -U test_user; do
        echo "PostgreSQL is unavailable - sleeping"
        sleep 2
      done
      echo "PostgreSQL is ready!"
    # Copy test environment file
    - cp .env.test .env
  script:
    # Run tests with coverage
    - npm run test:ci
  coverage: '/Lines\s*:\s*(\d+\.\d+)%/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: backend/coverage/cobertura-coverage.xml
      junit: backend/coverage/junit.xml
    paths:
      - backend/coverage/
    expire_in: 1 week
    when: always
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_COMMIT_BRANCH == "develop"

test-frontend:
  stage: test
  image: node:18-alpine
  parallel:
    matrix:
      - MODULE: merchant-portal
      - MODULE: admin-dashboard
  before_script:
    - cd $MODULE
    - npm ci
  script:
    - npm run test -- --coverage --watchAll=false
    - npm run lint
    - npm run type-check || echo "Type checking completed"
  artifacts:
    reports:
      junit: $MODULE/coverage/junit.xml
    paths:
      - $MODULE/coverage/
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_COMMIT_BRANCH == "develop"

# ===================================
# Stage: Build
# ===================================
.build-template: &build-template
  stage: build
  before_script:
    - echo $REGISTRY_PASSWORD | docker login -u $REGISTRY_USER --password-stdin $REGISTRY_URL
  after_script:
    - docker logout $REGISTRY_URL

build-backend:
  <<: *build-template
  script:
    - cd backend
    - docker build --target production -t $BACKEND_IMAGE:$CI_COMMIT_SHA -t $BACKEND_IMAGE:latest .
    - docker push $BACKEND_IMAGE:$CI_COMMIT_SHA
    - docker push $BACKEND_IMAGE:latest
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_COMMIT_BRANCH == "develop"
    - changes:
        - backend/**/*

build-merchant-portal:
  <<: *build-template
  script:
    - cd merchant-portal
    - docker build --target production -t $MERCHANT_PORTAL_IMAGE:$CI_COMMIT_SHA -t $MERCHANT_PORTAL_IMAGE:latest .
    - docker push $MERCHANT_PORTAL_IMAGE:$CI_COMMIT_SHA
    - docker push $MERCHANT_PORTAL_IMAGE:latest
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_COMMIT_BRANCH == "develop"
    - changes:
        - merchant-portal/**/*

build-admin-dashboard:
  <<: *build-template
  script:
    - cd admin-dashboard
    - docker build --target production -t $ADMIN_DASHBOARD_IMAGE:$CI_COMMIT_SHA -t $ADMIN_DASHBOARD_IMAGE:latest .
    - docker push $ADMIN_DASHBOARD_IMAGE:$CI_COMMIT_SHA
    - docker push $ADMIN_DASHBOARD_IMAGE:latest
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_COMMIT_BRANCH == "develop"
    - changes:
        - admin-dashboard/**/*

build-mobile-app:
  <<: *build-template
  script:
    - cd mobile-app
    - docker build --target production -t $MOBILE_APP_IMAGE:$CI_COMMIT_SHA -t $MOBILE_APP_IMAGE:latest .
    - docker push $MOBILE_APP_IMAGE:$CI_COMMIT_SHA
    - docker push $MOBILE_APP_IMAGE:latest
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_COMMIT_BRANCH == "develop"
    - changes:
        - mobile-app/**/*

# ===================================
# Stage: Security Scan
# ===================================
security-scan:
  stage: security-scan
  image: aquasec/trivy:latest
  script:
    - trivy image --exit-code 0 --severity HIGH,CRITICAL $BACKEND_IMAGE:$CI_COMMIT_SHA
    - trivy image --exit-code 0 --severity HIGH,CRITICAL $MERCHANT_PORTAL_IMAGE:$CI_COMMIT_SHA
    - trivy image --exit-code 0 --severity HIGH,CRITICAL $ADMIN_DASHBOARD_IMAGE:$CI_COMMIT_SHA
  artifacts:
    reports:
      container_scanning: gl-container-scanning-report.json
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_COMMIT_BRANCH == "develop"

# ===================================
# Stage: Deploy Staging
# ===================================
deploy-staging:
  stage: deploy-staging
  image: alpine:latest
  environment:
    name: staging
    url: https://staging.promodetect.com
  before_script:
    - apk add --no-cache openssh-client rsync bash
    - eval $(ssh-agent -s)
    - echo "$STAGING_SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -H $APP_VM_HOST >> ~/.ssh/known_hosts
    # Validate required environment variables
    - |
      if [ -z "$STAGING_DB_PASSWORD" ] || [ -z "$STAGING_REDIS_PASSWORD" ] || [ -z "$STAGING_JWT_SECRET" ]; then
        echo "ERROR: Required staging environment variables are not set"
        echo "Please set: STAGING_DB_PASSWORD, STAGING_REDIS_PASSWORD, STAGING_JWT_SECRET"
        exit 1
      fi
  script:
    - ssh promotun@$APP_VM_HOST "cd /opt/promotun && ./deployment/scripts/deploy-staging.sh $CI_COMMIT_SHA"
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
  when: manual

# ===================================
# Stage: Deploy Production
# ===================================
.deploy-production-template: &deploy-production-template
  stage: deploy-production
  image: alpine:latest
  environment:
    name: production
    url: https://promodetect.com
  before_script:
    - apk add --no-cache openssh-client rsync curl
    - eval $(ssh-agent -s)
    - echo "$PRODUCTION_SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -H $APP_VM_HOST >> ~/.ssh/known_hosts
    - ssh-keyscan -H $DB_VM_HOST >> ~/.ssh/known_hosts

deploy-production:
  <<: *deploy-production-template
  before_script:
    - apk add --no-cache openssh-client rsync curl bash
    - eval $(ssh-agent -s)
    - echo "$PRODUCTION_SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -H $APP_VM_HOST >> ~/.ssh/known_hosts
    - ssh-keyscan -H $DB_VM_HOST >> ~/.ssh/known_hosts
    # Validate required production environment variables
    - |
      required_vars="PRODUCTION_DB_PASSWORD PRODUCTION_REDIS_PASSWORD PRODUCTION_JWT_SECRET PRODUCTION_SESSION_SECRET PRODUCTION_NEXTAUTH_SECRET PRODUCTION_FIREBASE_PRIVATE_KEY PRODUCTION_SENDGRID_API_KEY PRODUCTION_GOOGLE_MAPS_API_KEY PRODUCTION_GRAFANA_PASSWORD"
      for var in $required_vars; do
        if [ -z "$(eval echo \$$var)" ]; then
          echo "ERROR: Required production environment variable $var is not set"
          exit 1
        fi
      done
      echo "All required production environment variables are set"
  script:
    # Create backup before deployment
    - ssh promotun@$APP_VM_HOST "cd /opt/promotun && ./deployment/scripts/create-backup.sh pre-deployment-$CI_COMMIT_SHA"

    # Deploy to production
    - ssh promotun@$APP_VM_HOST "cd /opt/promotun && ./deployment/scripts/deploy-production.sh $CI_COMMIT_SHA"

    # Verify deployment
    - sleep 60
    - curl -f https://$DOMAIN_NAME/health || exit 1
    - curl -f https://$DOMAIN_NAME/api/health || exit 1

    # Run post-deployment tests
    - ssh promotun@$APP_VM_HOST "cd /opt/promotun && ./deployment/scripts/post-deployment-tests.sh"
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
  when: manual

# Rollback job (manual trigger only)
rollback-production:
  <<: *deploy-production-template
  script:
    - ssh promotun@$APP_VM_HOST "cd /opt/promotun && ./deployment/scripts/rollback-deployment.sh $ROLLBACK_VERSION"
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
  when: manual
  variables:
    ROLLBACK_VERSION: "latest-stable"

# ===================================
# Stage: Cleanup
# ===================================
cleanup-registry:
  stage: cleanup
  image: alpine:latest
  before_script:
    - apk add --no-cache curl jq
  script:
    # Keep only last 10 images per service
    - echo "Cleaning up old Docker images from registry"
    # This would typically use GitLab Container Registry API
    - echo "Registry cleanup completed"
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_COMMIT_BRANCH == "develop"
  when: manual
