#!/bin/bash

# PromoTun Development Cleanup Script
# This script removes development files and prepares for production deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Files and directories to remove
CLEANUP_ITEMS=(
    # Development files
    "simple-server.js"
    "deploy-local.js"
    "start-promotun-docker.bat"
    
    # Development Docker files
    "deployment/docker-compose.yml"
    "deployment/docker-compose-minimal.yml"
    
    # Development environment files (keep templates)
    "backend/.env"
    "merchant-portal/.env.local"
    "deployment/.env"
    
    # Node modules (will be rebuilt in containers)
    "backend/node_modules"
    "merchant-portal/node_modules"
    "admin-dashboard/node_modules"
    "mobile-app/node_modules"
    
    # Build artifacts
    "backend/dist"
    "backend/build"
    "merchant-portal/.next"
    "merchant-portal/out"
    "admin-dashboard/.next"
    "admin-dashboard/out"
    
    # Log files
    "backend/logs"
    "*.log"
    
    # Temporary files
    "*.tmp"
    "*.temp"
    ".DS_Store"
    "Thumbs.db"
    
    # IDE files
    ".vscode/settings.json"
    ".idea"
    
    # Package lock files (will be regenerated)
    "package-lock.json"
    "yarn.lock"
    "pnpm-lock.yaml"
    
    # Test coverage
    "coverage"
    ".nyc_output"
    
    # Development certificates
    "deployment/nginx/ssl/localhost.*"
)

# Backup important files before cleanup
backup_important_files() {
    print_info "Creating backup of important files..."
    
    BACKUP_DIR="deployment/backup-$(date +%Y%m%d_%H%M%S)"
    mkdir -p "${BACKUP_DIR}"
    
    # Backup package.json files
    find . -name "package.json" -not -path "./node_modules/*" -exec cp --parents {} "${BACKUP_DIR}/" \;
    
    # Backup environment templates
    find . -name "*.env.example" -exec cp --parents {} "${BACKUP_DIR}/" \;
    
    # Backup important configuration files
    if [[ -f "deployment/.env.production" ]]; then
        cp "deployment/.env.production" "${BACKUP_DIR}/"
    fi
    
    print_status "Backup created in ${BACKUP_DIR}"
}

# Clean up development files
cleanup_files() {
    print_info "Cleaning up development files..."
    
    for item in "${CLEANUP_ITEMS[@]}"; do
        if [[ -e "$item" ]]; then
            if [[ -d "$item" ]]; then
                print_info "Removing directory: $item"
                rm -rf "$item"
            else
                print_info "Removing file: $item"
                rm -f "$item"
            fi
        fi
    done
    
    # Clean up empty directories
    find . -type d -empty -delete 2>/dev/null || true
    
    print_status "Development files cleaned up"
}

# Optimize Docker configurations
optimize_docker_configs() {
    print_info "Optimizing Docker configurations..."
    
    # Remove development docker-compose files
    rm -f deployment/docker-compose.yml
    rm -f deployment/docker-compose-minimal.yml
    
    # Rename production compose file to default
    if [[ -f "deployment/docker-compose.production.yml" ]]; then
        cp "deployment/docker-compose.production.yml" "deployment/docker-compose.yml"
        print_status "Production Docker Compose configuration set as default"
    fi
    
    # Clean up Dockerfile development stages
    for dockerfile in $(find . -name "Dockerfile" -not -path "./node_modules/*"); do
        if grep -q "development" "$dockerfile"; then
            print_info "Optimizing $dockerfile for production"
            # Remove development-specific content (this is a placeholder - customize as needed)
            sed -i '/# Development stage/,/# End development stage/d' "$dockerfile" 2>/dev/null || true
        fi
    done
    
    print_status "Docker configurations optimized"
}

# Update package.json files for production
optimize_package_json() {
    print_info "Optimizing package.json files for production..."
    
    # Remove development dependencies from package.json files
    for package_file in $(find . -name "package.json" -not -path "./node_modules/*"); do
        if [[ -f "$package_file" ]]; then
            print_info "Processing $package_file"
            
            # Create backup
            cp "$package_file" "$package_file.backup"
            
            # Remove development scripts (customize as needed)
            # This is a placeholder - you might want to keep some dev scripts
            # jq 'del(.scripts.dev, .scripts.test, .scripts.lint)' "$package_file" > "$package_file.tmp" && mv "$package_file.tmp" "$package_file"
        fi
    done
    
    print_status "Package.json files optimized"
}

# Create production-ready directory structure
create_production_structure() {
    print_info "Creating production directory structure..."
    
    # Create necessary directories
    mkdir -p deployment/{scripts,monitoring,nginx,ssl}
    mkdir -p logs
    mkdir -p backups
    
    # Set proper permissions
    chmod +x deployment/scripts/*.sh 2>/dev/null || true
    
    print_status "Production directory structure created"
}

# Generate production checklist
generate_checklist() {
    print_info "Generating production deployment checklist..."
    
    cat > "PRODUCTION_CHECKLIST.md" << 'EOF'
# PromoTun Production Deployment Checklist

## Pre-Deployment
- [ ] DNS configured (promodetect.com → **************)
- [ ] Database VM setup completed (**************)
- [ ] Application VM setup completed (**************)
- [ ] SSL certificates configured
- [ ] Environment variables configured
- [ ] External API keys configured
- [ ] Firewall rules applied

## Deployment
- [ ] Code deployed to /opt/promotun
- [ ] Environment file configured
- [ ] Docker images built successfully
- [ ] All containers started
- [ ] Health checks passing
- [ ] SSL working correctly

## Post-Deployment
- [ ] Monitoring dashboard accessible
- [ ] Backup system working
- [ ] Performance testing completed
- [ ] Security scan completed
- [ ] Documentation updated
- [ ] Team notified

## Verification URLs
- [ ] https://promodetect.com (Main site)
- [ ] https://promodetect.com/api/health (API health)
- [ ] https://promodetect.com/admin (Admin panel)
- [ ] https://promodetect.com/grafana (Monitoring)

## Emergency Contacts
- System Admin: [contact]
- Database Admin: [contact]
- Development Team: [contact]
EOF
    
    print_status "Production checklist created"
}

# Verify cleanup
verify_cleanup() {
    print_info "Verifying cleanup..."
    
    # Check for remaining development files
    REMAINING_DEV_FILES=()
    
    for item in "${CLEANUP_ITEMS[@]}"; do
        if [[ -e "$item" ]]; then
            REMAINING_DEV_FILES+=("$item")
        fi
    done
    
    if [[ ${#REMAINING_DEV_FILES[@]} -eq 0 ]]; then
        print_status "All development files cleaned up successfully"
    else
        print_warning "Some development files remain:"
        for file in "${REMAINING_DEV_FILES[@]}"; do
            echo "  - $file"
        done
    fi
    
    # Check for sensitive files
    SENSITIVE_FILES=(
        "backend/.env"
        "deployment/.env"
        "*.key"
        "*.pem"
        "*password*"
    )
    
    print_info "Checking for sensitive files..."
    for pattern in "${SENSITIVE_FILES[@]}"; do
        if ls $pattern 2>/dev/null; then
            print_warning "Sensitive file found: $pattern"
        fi
    done
    
    print_status "Cleanup verification completed"
}

# Main execution
main() {
    print_info "Starting PromoTun production cleanup..."
    echo
    
    # Confirm cleanup
    print_warning "This will remove development files and prepare for production deployment."
    read -p "Continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Cleanup cancelled"
        exit 0
    fi
    
    backup_important_files
    cleanup_files
    optimize_docker_configs
    optimize_package_json
    create_production_structure
    generate_checklist
    verify_cleanup
    
    print_status "Production cleanup completed successfully!"
    echo
    print_info "Next steps:"
    echo "1. Review PRODUCTION_CHECKLIST.md"
    echo "2. Configure production environment variables"
    echo "3. Deploy to production servers"
    echo "4. Run deployment verification"
    echo
    print_warning "Backup created in deployment/backup-* directory"
}

# Run main function
main "$@"
