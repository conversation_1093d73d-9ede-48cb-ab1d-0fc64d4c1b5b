# GitLab Repository Setup Guide for PromoTun

## 🚀 Repository Creation Steps

### 1. Create GitLab Repository

1. **Log in to GitLab** (gitlab.com or your GitLab instance)

2. **Create New Project**
   - Click "New project" button
   - Choose "Create blank project"

3. **Configure Repository**
   ```
   Project name: PromoTun
   Project slug: promotun-platform
   Project description: Comprehensive promotion discovery platform connecting consumers with local deals and providing merchant management tools
   Visibility Level: Private (recommended for proprietary code)
   ```

4. **Initialize Repository**
   - ✅ Initialize repository with a README
   - ❌ Do NOT add .gitignore (we have our own)
   - ❌ Do NOT add license (we have our own)

### 2. Local Git Setup

```bash
# Navigate to your project directory
cd /path/to/PromoTun

# Initialize git repository (if not already done)
git init

# Add all files to staging
git add .

# Create initial commit
git commit -m "feat: initialize PromoTun platform repository

- Add comprehensive .gitignore for Node.js, React Native, Docker
- Add LICENSE (Proprietary)
- Add README.md with project overview and current status
- Add CONTRIBUTING.md with development guidelines
- Add setup scripts for Windows and Unix systems
- Add repository verification and GitLab setup guides"

# Add GitLab remote (replace with your actual repository URL)
git remote add origin https://gitlab.com/yourusername/promotun-platform.git

# Push to GitLab
git push -u origin main
```

### 3. Repository Configuration

#### Branch Protection Rules
1. Go to **Settings > Repository > Push Rules**
2. Configure main branch protection:
   ```
   ✅ Prevent committing secrets to Git
   ✅ Restrict commits by author (email)
   ✅ Prohibited file names: *.env, *.key, *.pem
   ```

3. Go to **Settings > Repository > Protected Branches**
4. Protect main branch:
   ```
   Branch: main
   ✅ Allowed to merge: Maintainers
   ✅ Allowed to push: No one
   ✅ Require approval from code owners
   ```

#### Project Settings
1. **General Settings**
   ```
   Project avatar: Upload PromoTun logo
   Project description: Update with detailed description
   Topics: promotion, deals, mobile-app, react-native, nodejs, nextjs, typescript
   ```

2. **Visibility and Access**
   ```
   Project visibility: Private
   Repository: Enabled
   Issues: Enabled
   Merge Requests: Enabled
   Wiki: Enabled
   Snippets: Disabled
   ```

### 4. Issue Templates

Create issue templates in `.gitlab/issue_templates/`:

#### Bug Report Template
```markdown
## Bug Description
A clear and concise description of what the bug is.

## Steps to Reproduce
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## Expected Behavior
A clear and concise description of what you expected to happen.

## Actual Behavior
A clear and concise description of what actually happened.

## Environment
- OS: [e.g. Windows 10, macOS 12.0, Ubuntu 20.04]
- Node.js version: [e.g. 16.14.0]
- Browser: [e.g. Chrome 98, Safari 15]
- Component: [e.g. Backend API, Merchant Portal, Mobile App]

## Screenshots
If applicable, add screenshots to help explain your problem.

## Additional Context
Add any other context about the problem here.
```

#### Feature Request Template
```markdown
## Feature Description
A clear and concise description of the feature you'd like to see.

## Problem Statement
What problem does this feature solve? What use case does it address?

## Proposed Solution
Describe the solution you'd like to see implemented.

## Alternative Solutions
Describe any alternative solutions or features you've considered.

## Implementation Notes
Any technical considerations or implementation details.

## Acceptance Criteria
- [ ] Criterion 1
- [ ] Criterion 2
- [ ] Criterion 3

## Additional Context
Add any other context, mockups, or examples about the feature request here.
```

### 5. Merge Request Templates

Create `.gitlab/merge_request_templates/Default.md`:

```markdown
## Description
Brief description of changes made in this merge request.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Performance improvement
- [ ] Code refactoring

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] No console errors

## Checklist
- [ ] Code follows project style guidelines
- [ ] Self-review of code completed
- [ ] Code is commented, particularly in hard-to-understand areas
- [ ] Documentation has been updated
- [ ] No sensitive data is committed

## Screenshots (if applicable)
Add screenshots to help explain your changes.

## Related Issues
Closes #(issue number)
```

### 6. CI/CD Pipeline Setup (Optional)

Create `.gitlab-ci.yml`:

```yaml
stages:
  - test
  - build
  - deploy

variables:
  NODE_VERSION: "16"

# Backend Tests
backend-test:
  stage: test
  image: node:${NODE_VERSION}
  script:
    - cd backend
    - npm ci
    - npm run test
  only:
    changes:
      - backend/**/*

# Merchant Portal Tests
frontend-test:
  stage: test
  image: node:${NODE_VERSION}
  script:
    - cd merchant-portal
    - npm ci
    - npm run test
  only:
    changes:
      - merchant-portal/**/*

# Build Docker Images
build:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker build -t promotun-backend ./backend
    - docker build -t promotun-frontend ./merchant-portal
  only:
    - main
    - develop
```

### 7. Project Labels

Create labels for issue and MR organization:

#### Type Labels
- `type::bug` (Red)
- `type::feature` (Blue)
- `type::documentation` (Green)
- `type::maintenance` (Gray)

#### Priority Labels
- `priority::critical` (Dark Red)
- `priority::high` (Orange)
- `priority::medium` (Yellow)
- `priority::low` (Light Blue)

#### Component Labels
- `component::backend` (Purple)
- `component::frontend` (Cyan)
- `component::mobile` (Pink)
- `component::deployment` (Brown)

#### Status Labels
- `status::in-progress` (Blue)
- `status::needs-review` (Orange)
- `status::blocked` (Red)
- `status::ready` (Green)

### 8. Wiki Setup

Create initial wiki pages:

1. **Home** - Project overview and navigation
2. **Development Setup** - Detailed setup instructions
3. **API Documentation** - API endpoint documentation
4. **Deployment Guide** - Production deployment instructions
5. **Troubleshooting** - Common issues and solutions

### 9. Security Configuration

1. **Secret Detection**
   - Enable secret detection in CI/CD
   - Configure secret detection rules

2. **Dependency Scanning**
   - Enable dependency scanning
   - Configure vulnerability alerts

3. **Access Tokens**
   - Create project access tokens for CI/CD
   - Set appropriate scopes and expiration

### 10. Team Setup

1. **Add Team Members**
   ```
   Maintainer: Project leads
   Developer: Core developers
   Reporter: QA team, stakeholders
   ```

2. **Code Owners**
   Create `.gitlab/CODEOWNERS`:
   ```
   # Global owners
   * @project-lead

   # Backend code
   backend/ @backend-team

   # Frontend code
   merchant-portal/ @frontend-team
   admin-dashboard/ @frontend-team

   # Mobile app
   mobile-app/ @mobile-team

   # Deployment and infrastructure
   deployment/ @devops-team
   ```

## ✅ Verification Checklist

After setup, verify:

- [ ] Repository is created and accessible
- [ ] Initial commit is pushed successfully
- [ ] Branch protection rules are configured
- [ ] Issue and MR templates are in place
- [ ] Labels are created and organized
- [ ] Team members have appropriate access
- [ ] CI/CD pipeline is configured (if applicable)
- [ ] Wiki pages are created
- [ ] Security features are enabled

## 🎉 Next Steps

1. **Share repository access** with team members
2. **Create first issues** for immediate tasks
3. **Set up development environment** using setup scripts
4. **Begin development workflow** following CONTRIBUTING.md
5. **Plan first release** using the branching strategy

Your PromoTun repository is now ready for collaborative development! 🚀
