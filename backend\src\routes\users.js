const express = require('express');
const { body, validationResult } = require('express-validator');
const { query } = require('../database/connection');
const { authenticateToken, requireVerification } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Get user profile
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const userResult = await query(`
      SELECT 
        u.id, u.email, u.first_name, u.last_name, u.user_type, 
        u.preferred_language, u.is_verified, u.created_at,
        up.avatar_url, up.date_of_birth, up.gender, up.location_lat, 
        up.location_lng, up.address, up.city, up.country,
        up.notification_preferences, up.privacy_settings
      FROM users u
      LEFT JOIN user_profiles up ON u.id = up.user_id
      WHERE u.id = $1
    `, [req.user.id]);

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const user = userResult.rows[0];
    
    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          userType: user.user_type,
          preferredLanguage: user.preferred_language,
          isVerified: user.is_verified,
          createdAt: user.created_at,
          profile: {
            avatarUrl: user.avatar_url,
            dateOfBirth: user.date_of_birth,
            gender: user.gender,
            location: {
              lat: user.location_lat,
              lng: user.location_lng
            },
            address: user.address,
            city: user.city,
            country: user.country,
            notificationPreferences: user.notification_preferences || {},
            privacySettings: user.privacy_settings || {}
          }
        }
      }
    });
  } catch (error) {
    logger.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Update user profile
router.put('/profile', [
  authenticateToken,
  body('firstName').optional().trim().isLength({ min: 2, max: 50 }),
  body('lastName').optional().trim().isLength({ min: 2, max: 50 }),
  body('phone').optional().isMobilePhone(),
  body('preferredLanguage').optional().isIn(['en', 'fr', 'ar']),
  body('dateOfBirth').optional().isISO8601(),
  body('gender').optional().isIn(['male', 'female', 'other']),
  body('address').optional().trim().isLength({ max: 500 }),
  body('city').optional().trim().isLength({ max: 100 }),
  body('country').optional().trim().isLength({ max: 100 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const {
      firstName, lastName, phone, preferredLanguage,
      dateOfBirth, gender, address, city, country,
      locationLat, locationLng, notificationPreferences,
      privacySettings
    } = req.body;

    // Update user table
    if (firstName || lastName || phone || preferredLanguage) {
      await query(`
        UPDATE users 
        SET 
          first_name = COALESCE($1, first_name),
          last_name = COALESCE($2, last_name),
          phone = COALESCE($3, phone),
          preferred_language = COALESCE($4, preferred_language)
        WHERE id = $5
      `, [firstName, lastName, phone, preferredLanguage, req.user.id]);
    }

    // Update user profile
    await query(`
      INSERT INTO user_profiles (
        user_id, date_of_birth, gender, location_lat, location_lng,
        address, city, country, notification_preferences, privacy_settings
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      ON CONFLICT (user_id) DO UPDATE SET
        date_of_birth = COALESCE($2, user_profiles.date_of_birth),
        gender = COALESCE($3, user_profiles.gender),
        location_lat = COALESCE($4, user_profiles.location_lat),
        location_lng = COALESCE($5, user_profiles.location_lng),
        address = COALESCE($6, user_profiles.address),
        city = COALESCE($7, user_profiles.city),
        country = COALESCE($8, user_profiles.country),
        notification_preferences = COALESCE($9, user_profiles.notification_preferences),
        privacy_settings = COALESCE($10, user_profiles.privacy_settings),
        updated_at = CURRENT_TIMESTAMP
    `, [
      req.user.id, dateOfBirth, gender, locationLat, locationLng,
      address, city, country, 
      notificationPreferences ? JSON.stringify(notificationPreferences) : null,
      privacySettings ? JSON.stringify(privacySettings) : null
    ]);

    // Get updated user data
    const updatedUser = await query(`
      SELECT 
        u.id, u.email, u.first_name, u.last_name, u.user_type, 
        u.preferred_language, u.is_verified
      FROM users u
      WHERE u.id = $1
    `, [req.user.id]);

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: {
        user: {
          id: updatedUser.rows[0].id,
          email: updatedUser.rows[0].email,
          firstName: updatedUser.rows[0].first_name,
          lastName: updatedUser.rows[0].last_name,
          userType: updatedUser.rows[0].user_type,
          preferredLanguage: updatedUser.rows[0].preferred_language,
          isVerified: updatedUser.rows[0].is_verified
        }
      }
    });
  } catch (error) {
    logger.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get user favorites
router.get('/favorites', authenticateToken, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;

    const favoritesResult = await query(`
      SELECT 
        p.id, p.title, p.description, p.original_price, p.discounted_price,
        p.discount_percentage, p.start_date, p.end_date, p.is_active,
        mp.business_name as merchant_name,
        c.name as category_name,
        pi.image_url,
        uf.created_at as favorited_at
      FROM user_favorites uf
      JOIN promotions p ON uf.promotion_id = p.id
      LEFT JOIN merchant_profiles mp ON p.merchant_id = mp.id
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN promotion_images pi ON p.id = pi.promotion_id AND pi.is_primary = true
      WHERE uf.user_id = $1
      ORDER BY uf.created_at DESC
      LIMIT $2 OFFSET $3
    `, [req.user.id, limit, offset]);

    const countResult = await query(
      'SELECT COUNT(*) FROM user_favorites WHERE user_id = $1',
      [req.user.id]
    );

    const total = parseInt(countResult.rows[0].count);
    const pages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: {
        favorites: favoritesResult.rows,
        pagination: {
          page,
          limit,
          total,
          pages
        }
      }
    });
  } catch (error) {
    logger.error('Get favorites error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Add to favorites
router.post('/favorites', [
  authenticateToken,
  requireVerification,
  body('promotionId').isUUID()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { promotionId } = req.body;

    // Check if promotion exists
    const promotionResult = await query(
      'SELECT id FROM promotions WHERE id = $1',
      [promotionId]
    );

    if (promotionResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Promotion not found'
      });
    }

    // Add to favorites
    await query(`
      INSERT INTO user_favorites (user_id, promotion_id)
      VALUES ($1, $2)
      ON CONFLICT (user_id, promotion_id) DO NOTHING
    `, [req.user.id, promotionId]);

    res.status(201).json({
      success: true,
      message: 'Added to favorites'
    });
  } catch (error) {
    logger.error('Add to favorites error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Remove from favorites
router.delete('/favorites/:promotionId', authenticateToken, async (req, res) => {
  try {
    const { promotionId } = req.params;

    const result = await query(
      'DELETE FROM user_favorites WHERE user_id = $1 AND promotion_id = $2',
      [req.user.id, promotionId]
    );

    if (result.rowCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'Favorite not found'
      });
    }

    res.json({
      success: true,
      message: 'Removed from favorites'
    });
  } catch (error) {
    logger.error('Remove from favorites error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
