const nodemailer = require('nodemailer');
const logger = require('../utils/logger');

// Create transporter
const createTransporter = () => {
  if (process.env.SENDGRID_API_KEY) {
    // SendGrid configuration
    return nodemailer.createTransporter({
      service: 'SendGrid',
      auth: {
        user: 'apikey',
        pass: process.env.SENDGRID_API_KEY
      }
    });
  } else {
    // Development configuration (using Ethereal Email)
    return nodemailer.createTransporter({
      host: 'smtp.ethereal.email',
      port: 587,
      auth: {
        user: '<EMAIL>',
        pass: 'ethereal.pass'
      }
    });
  }
};

/**
 * Send email
 */
async function sendEmail({ to, subject, template, data, html, text }) {
  try {
    const transporter = createTransporter();

    let emailHtml = html;
    let emailText = text;

    // Generate email content based on template
    if (template && data) {
      const emailContent = generateEmailContent(template, data);
      emailHtml = emailContent.html;
      emailText = emailContent.text;
    }

    const mailOptions = {
      from: `${process.env.FROM_NAME || 'PromoTun'} <${process.env.FROM_EMAIL || '<EMAIL>'}>`,
      to,
      subject,
      html: emailHtml,
      text: emailText
    };

    const result = await transporter.sendMail(mailOptions);
    
    logger.info(`Email sent successfully to ${to}`, {
      messageId: result.messageId,
      template,
      subject
    });

    return { success: true, messageId: result.messageId };
  } catch (error) {
    logger.error('Email sending failed:', error);
    throw error;
  }
}

/**
 * Generate email content based on template
 */
function generateEmailContent(template, data) {
  const templates = {
    verification: {
      subject: 'Verify your PromoTun account',
      html: generateVerificationEmailHTML(data),
      text: generateVerificationEmailText(data)
    },
    passwordReset: {
      subject: 'Reset your PromoTun password',
      html: generatePasswordResetEmailHTML(data),
      text: generatePasswordResetEmailText(data)
    },
    welcome: {
      subject: 'Welcome to PromoTun!',
      html: generateWelcomeEmailHTML(data),
      text: generateWelcomeEmailText(data)
    },
    promotionAlert: {
      subject: 'New promotion near you!',
      html: generatePromotionAlertEmailHTML(data),
      text: generatePromotionAlertEmailText(data)
    }
  };

  return templates[template] || { html: '', text: '' };
}

/**
 * Generate verification email HTML
 */
function generateVerificationEmailHTML(data) {
  const { firstName, verificationToken, language = 'en' } = data;
  
  const messages = {
    en: {
      greeting: `Hello ${firstName}`,
      message: 'Thank you for registering with PromoTun! Please verify your email address by clicking the button below:',
      button: 'Verify Email',
      footer: 'If you did not create this account, please ignore this email.',
      company: 'PromoTun Team'
    },
    fr: {
      greeting: `Bonjour ${firstName}`,
      message: 'Merci de vous être inscrit sur PromoTun ! Veuillez vérifier votre adresse e-mail en cliquant sur le bouton ci-dessous :',
      button: 'Vérifier l\'e-mail',
      footer: 'Si vous n\'avez pas créé ce compte, veuillez ignorer cet e-mail.',
      company: 'Équipe PromoTun'
    },
    ar: {
      greeting: `مرحباً ${firstName}`,
      message: 'شكراً لتسجيلك في بروموتون! يرجى التحقق من عنوان بريدك الإلكتروني بالنقر على الزر أدناه:',
      button: 'تحقق من البريد الإلكتروني',
      footer: 'إذا لم تقم بإنشاء هذا الحساب، يرجى تجاهل هذا البريد الإلكتروني.',
      company: 'فريق بروموتون'
    }
  };

  const msg = messages[language] || messages.en;
  const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${verificationToken}`;

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Email Verification</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #FF6B35; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .button { display: inline-block; padding: 12px 24px; background: #FF6B35; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { padding: 20px; text-align: center; color: #666; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>PromoTun</h1>
        </div>
        <div class="content">
          <h2>${msg.greeting}!</h2>
          <p>${msg.message}</p>
          <a href="${verificationUrl}" class="button">${msg.button}</a>
          <p>${msg.footer}</p>
        </div>
        <div class="footer">
          <p>${msg.company}</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

/**
 * Generate verification email text
 */
function generateVerificationEmailText(data) {
  const { firstName, verificationToken } = data;
  const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${verificationToken}`;

  return `
Hello ${firstName}!

Thank you for registering with PromoTun! Please verify your email address by visiting the following link:

${verificationUrl}

If you did not create this account, please ignore this email.

Best regards,
PromoTun Team
  `;
}

/**
 * Generate password reset email HTML
 */
function generatePasswordResetEmailHTML(data) {
  const { firstName, resetToken } = data;
  const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Password Reset</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .button { display: inline-block; padding: 12px 24px; background: #FF6B35; color: white; text-decoration: none; border-radius: 5px; }
      </style>
    </head>
    <body>
      <div class="container">
        <h2>Password Reset Request</h2>
        <p>Hello ${firstName},</p>
        <p>You requested a password reset for your PromoTun account. Click the button below to reset your password:</p>
        <a href="${resetUrl}" class="button">Reset Password</a>
        <p>If you did not request this reset, please ignore this email.</p>
        <p>Best regards,<br>PromoTun Team</p>
      </div>
    </body>
    </html>
  `;
}

/**
 * Generate password reset email text
 */
function generatePasswordResetEmailText(data) {
  const { firstName, resetToken } = data;
  const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;

  return `
Hello ${firstName},

You requested a password reset for your PromoTun account. Visit the following link to reset your password:

${resetUrl}

If you did not request this reset, please ignore this email.

Best regards,
PromoTun Team
  `;
}

/**
 * Generate welcome email HTML
 */
function generateWelcomeEmailHTML(data) {
  const { firstName } = data;

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Welcome to PromoTun</title>
    </head>
    <body>
      <h2>Welcome to PromoTun, ${firstName}!</h2>
      <p>We're excited to have you on board. Start discovering amazing deals and promotions near you!</p>
      <p>Best regards,<br>PromoTun Team</p>
    </body>
    </html>
  `;
}

/**
 * Generate welcome email text
 */
function generateWelcomeEmailText(data) {
  const { firstName } = data;

  return `
Welcome to PromoTun, ${firstName}!

We're excited to have you on board. Start discovering amazing deals and promotions near you!

Best regards,
PromoTun Team
  `;
}

/**
 * Generate promotion alert email HTML
 */
function generatePromotionAlertEmailHTML(data) {
  const { firstName, promotion } = data;

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>New Promotion Alert</title>
    </head>
    <body>
      <h2>New Promotion Near You!</h2>
      <p>Hello ${firstName},</p>
      <p>We found a new promotion that might interest you:</p>
      <h3>${promotion.title}</h3>
      <p>${promotion.description}</p>
      <p>Discount: ${promotion.discount}%</p>
      <p>Valid until: ${promotion.endDate}</p>
      <p>Best regards,<br>PromoTun Team</p>
    </body>
    </html>
  `;
}

/**
 * Generate promotion alert email text
 */
function generatePromotionAlertEmailText(data) {
  const { firstName, promotion } = data;

  return `
New Promotion Near You!

Hello ${firstName},

We found a new promotion that might interest you:

${promotion.title}
${promotion.description}

Discount: ${promotion.discount}%
Valid until: ${promotion.endDate}

Best regards,
PromoTun Team
  `;
}

module.exports = {
  sendEmail
};
