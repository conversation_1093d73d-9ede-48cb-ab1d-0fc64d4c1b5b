const express = require('express');
const { body, validationResult } = require('express-validator');
const { query } = require('../database/connection');
const { authenticateToken, requireMerchant } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Get promotion feedback
router.get('/:promotionId', async (req, res) => {
  try {
    const { promotionId } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = Math.min(parseInt(req.query.limit) || 20, 100);
    const offset = (page - 1) * limit;

    const feedbackResult = await query(`
      SELECT 
        uf.id, uf.rating, uf.comment_type, uf.comment, uf.is_anonymous,
        uf.created_at, u.first_name, u.last_name,
        mr.response, mr.created_at as response_date
      FROM user_feedback uf
      LEFT JOIN users u ON uf.user_id = u.id
      LEFT JOIN merchant_responses mr ON uf.id = mr.feedback_id
      WHERE uf.promotion_id = $1
      ORDER BY uf.created_at DESC
      LIMIT $2 OFFSET $3
    `, [promotionId, limit, offset]);

    const countResult = await query(
      'SELECT COUNT(*) FROM user_feedback WHERE promotion_id = $1',
      [promotionId]
    );

    const total = parseInt(countResult.rows[0].count);
    const pages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: {
        feedback: feedbackResult.rows,
        pagination: { page, limit, total, pages }
      }
    });
  } catch (error) {
    logger.error('Get feedback error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Add feedback
router.post('/:promotionId', [
  authenticateToken,
  body('rating').isInt({ min: 1, max: 5 }),
  body('commentType').isIn(['positive', 'negative', 'neutral', 'question']),
  body('comment').optional().trim().isLength({ max: 1000 }),
  body('isAnonymous').optional().isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { promotionId } = req.params;
    const { rating, commentType, comment, isAnonymous = false } = req.body;

    const feedbackResult = await query(`
      INSERT INTO user_feedback (
        user_id, promotion_id, rating, comment_type, comment, is_anonymous
      ) VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `, [req.user.id, promotionId, rating, commentType, comment, isAnonymous]);

    res.status(201).json({
      success: true,
      message: 'Feedback added successfully',
      data: { feedback: feedbackResult.rows[0] }
    });
  } catch (error) {
    logger.error('Add feedback error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Respond to feedback (merchant only)
router.post('/:feedbackId/response', [
  authenticateToken,
  requireMerchant,
  body('response').trim().isLength({ min: 1, max: 1000 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { feedbackId } = req.params;
    const { response } = req.body;

    // Get merchant ID
    const merchantResult = await query(
      'SELECT id FROM merchant_profiles WHERE user_id = $1',
      [req.user.id]
    );

    if (merchantResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Merchant profile not found'
      });
    }

    const merchantId = merchantResult.rows[0].id;

    const responseResult = await query(`
      INSERT INTO merchant_responses (feedback_id, merchant_id, response)
      VALUES ($1, $2, $3)
      RETURNING *
    `, [feedbackId, merchantId, response]);

    res.status(201).json({
      success: true,
      message: 'Response added successfully',
      data: { response: responseResult.rows[0] }
    });
  } catch (error) {
    logger.error('Add response error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
