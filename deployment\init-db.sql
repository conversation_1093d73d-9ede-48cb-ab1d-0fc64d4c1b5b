-- PromoTun Database Initialization Script
-- This script will be executed when the PostgreSQL container starts for the first time

-- Create database if it doesn't exist (handled by POSTGRES_DB environment variable)

-- Connect to the promotun database
\c promotun;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- <PERSON><PERSON> updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    phone VARCHAR(20),
    user_type VARCHAR(20) NOT NULL CHECK (user_type IN ('consumer', 'merchant', 'admin')),
    preferred_language VARCHAR(5) DEFAULT 'en' CHECK (preferred_language IN ('en', 'fr', 'ar')),
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    email_verification_token VARCHAR(255),
    email_verification_expires TIMESTAMP,
    password_reset_token VARCHAR(255),
    password_reset_expires TIMESTAMP,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create trigger for users table
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- User profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    avatar_url VARCHAR(500),
    date_of_birth DATE,
    gender VARCHAR(10) CHECK (gender IN ('male', 'female', 'other')),
    location_lat DECIMAL(10, 8),
    location_lng DECIMAL(11, 8),
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    country VARCHAR(100),
    postal_code VARCHAR(20),
    notification_preferences JSONB DEFAULT '{"push": true, "email": true, "sms": false}',
    privacy_settings JSONB DEFAULT '{"profile_public": false, "location_sharing": true}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id)
);

-- Create trigger for user_profiles table
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    name_fr VARCHAR(100),
    name_ar VARCHAR(100),
    description TEXT,
    description_fr TEXT,
    description_ar TEXT,
    parent_id UUID REFERENCES categories(id),
    icon_url VARCHAR(500),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create trigger for categories table
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Merchant profiles table
CREATE TABLE IF NOT EXISTS merchant_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    business_name VARCHAR(255) NOT NULL,
    business_type VARCHAR(100),
    description TEXT,
    description_fr TEXT,
    description_ar TEXT,
    logo_url VARCHAR(500),
    cover_image_url VARCHAR(500),
    website_url VARCHAR(500),
    business_registration_number VARCHAR(100),
    tax_id VARCHAR(100),
    subscription_plan VARCHAR(50) DEFAULT 'free' CHECK (subscription_plan IN ('free', 'basic', 'premium', 'enterprise')),
    subscription_expires TIMESTAMP,
    is_verified BOOLEAN DEFAULT FALSE,
    verification_documents JSONB,
    business_hours JSONB,
    social_media JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id)
);

-- Create trigger for merchant_profiles table
CREATE TRIGGER update_merchant_profiles_updated_at BEFORE UPDATE ON merchant_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Business locations table
CREATE TABLE IF NOT EXISTS business_locations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    merchant_id UUID REFERENCES merchant_profiles(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    address TEXT NOT NULL,
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100),
    country VARCHAR(100) NOT NULL,
    postal_code VARCHAR(20),
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(255),
    is_primary BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    operating_hours JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create trigger for business_locations table
CREATE TRIGGER update_business_locations_updated_at BEFORE UPDATE ON business_locations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create spatial index for location queries
CREATE INDEX IF NOT EXISTS idx_business_locations_coordinates 
ON business_locations USING GIST (ST_Point(longitude, latitude));

-- Promotions table
CREATE TABLE IF NOT EXISTS promotions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    merchant_id UUID REFERENCES merchant_profiles(id) ON DELETE CASCADE,
    category_id UUID REFERENCES categories(id),
    location_id UUID REFERENCES business_locations(id),
    title VARCHAR(255) NOT NULL,
    title_fr VARCHAR(255),
    title_ar VARCHAR(255),
    description TEXT NOT NULL,
    description_fr TEXT,
    description_ar TEXT,
    original_price DECIMAL(10, 2),
    discounted_price DECIMAL(10, 2),
    discount_percentage INTEGER,
    discount_amount DECIMAL(10, 2),
    promotion_type VARCHAR(50) NOT NULL CHECK (promotion_type IN ('percentage', 'fixed_amount', 'buy_one_get_one', 'free_shipping')),
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP NOT NULL,
    terms_conditions TEXT,
    terms_conditions_fr TEXT,
    terms_conditions_ar TEXT,
    max_redemptions INTEGER,
    current_redemptions INTEGER DEFAULT 0,
    is_featured BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'expired')),
    rejection_reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create trigger for promotions table
CREATE TRIGGER update_promotions_updated_at BEFORE UPDATE ON promotions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for promotions
CREATE INDEX IF NOT EXISTS idx_promotions_dates ON promotions (start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_promotions_merchant ON promotions (merchant_id);
CREATE INDEX IF NOT EXISTS idx_promotions_category ON promotions (category_id);
CREATE INDEX IF NOT EXISTS idx_promotions_status ON promotions (status, is_active);

-- Insert default categories
INSERT INTO categories (name, name_fr, name_ar, description, sort_order) VALUES
('Electronics', 'Électronique', 'إلكترونيات', 'Electronic devices and gadgets', 1),
('Fashion', 'Mode', 'أزياء', 'Clothing, shoes, and accessories', 2),
('Food & Dining', 'Alimentation', 'طعام ومطاعم', 'Restaurants, cafes, and food delivery', 3),
('Health & Beauty', 'Santé et Beauté', 'صحة وجمال', 'Healthcare, cosmetics, and wellness', 4),
('Home & Garden', 'Maison et Jardin', 'منزل وحديقة', 'Home improvement and gardening', 5),
('Sports & Fitness', 'Sport et Fitness', 'رياضة ولياقة', 'Sports equipment and fitness services', 6),
('Travel & Tourism', 'Voyage et Tourisme', 'سفر وسياحة', 'Hotels, flights, and travel services', 7),
('Automotive', 'Automobile', 'سيارات', 'Car services and automotive products', 8),
('Entertainment', 'Divertissement', 'ترفيه', 'Movies, games, and entertainment', 9),
('Education', 'Éducation', 'تعليم', 'Educational services and courses', 10)
ON CONFLICT DO NOTHING;

-- Create admin user
INSERT INTO users (email, password_hash, first_name, last_name, user_type, is_verified) VALUES
('<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Dh/Ey2', 'Admin', 'User', 'admin', true)
ON CONFLICT (email) DO NOTHING;

-- Create sample merchant user
INSERT INTO users (email, password_hash, first_name, last_name, user_type, is_verified) VALUES
('<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Dh/Ey2', 'Sample', 'Merchant', 'merchant', true)
ON CONFLICT (email) DO NOTHING;

-- Create sample consumer user
INSERT INTO users (email, password_hash, first_name, last_name, user_type, is_verified) VALUES
('<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Dh/Ey2', 'Sample', 'Consumer', 'consumer', true)
ON CONFLICT (email) DO NOTHING;

-- Grant permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;

-- Display success message
SELECT 'PromoTun database initialized successfully!' as message;
