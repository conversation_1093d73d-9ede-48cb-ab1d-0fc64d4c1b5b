const express = require('express');
const { body, query: queryValidator, validationResult } = require('express-validator');
const { query } = require('../database/connection');
const { authenticateToken, requireMerchant, optionalAuth } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Get promotions with filtering and pagination
router.get('/', optionalAuth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = Math.min(parseInt(req.query.limit) || 20, 100);
    const offset = (page - 1) * limit;
    
    const {
      category,
      lat,
      lng,
      radius = 10,
      search,
      sortBy = 'created_at',
      sortOrder = 'desc',
      merchantId,
      isActive = true
    } = req.query;

    let whereConditions = ['p.status = $1'];
    let queryParams = ['approved'];
    let paramIndex = 2;

    if (isActive) {
      whereConditions.push('p.is_active = true');
      whereConditions.push('p.start_date <= CURRENT_TIMESTAMP');
      whereConditions.push('p.end_date >= CURRENT_TIMESTAMP');
    }

    if (category) {
      whereConditions.push(`p.category_id = $${paramIndex}`);
      queryParams.push(category);
      paramIndex++;
    }

    if (merchantId) {
      whereConditions.push(`p.merchant_id = $${paramIndex}`);
      queryParams.push(merchantId);
      paramIndex++;
    }

    if (search) {
      whereConditions.push(`(p.title ILIKE $${paramIndex} OR p.description ILIKE $${paramIndex})`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    let distanceSelect = '';
    let distanceOrder = '';
    
    if (lat && lng) {
      distanceSelect = `, (
        6371 * acos(
          cos(radians($${paramIndex})) * cos(radians(bl.latitude)) * 
          cos(radians(bl.longitude) - radians($${paramIndex + 1})) + 
          sin(radians($${paramIndex})) * sin(radians(bl.latitude))
        )
      ) as distance`;
      
      queryParams.push(parseFloat(lat), parseFloat(lng));
      paramIndex += 2;

      if (radius) {
        whereConditions.push(`(
          6371 * acos(
            cos(radians($${paramIndex})) * cos(radians(bl.latitude)) * 
            cos(radians(bl.longitude) - radians($${paramIndex + 1})) + 
            sin(radians($${paramIndex})) * sin(radians(bl.latitude))
          )
        ) <= $${paramIndex + 2}`);
        
        queryParams.push(parseFloat(lat), parseFloat(lng), parseFloat(radius));
        paramIndex += 3;
      }

      if (sortBy === 'distance') {
        distanceOrder = 'distance';
      }
    }

    const validSortFields = ['created_at', 'start_date', 'discount_percentage', 'distance'];
    const orderBy = validSortFields.includes(sortBy) ? sortBy : 'created_at';
    const order = sortOrder.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

    const promotionsQuery = `
      SELECT 
        p.id, p.title, p.description, p.original_price, p.discounted_price,
        p.discount_percentage, p.discount_amount, p.promotion_type,
        p.start_date, p.end_date, p.terms_conditions, p.is_featured,
        mp.business_name as merchant_name,
        mp.logo_url as merchant_logo,
        c.name as category_name,
        c.name_fr as category_name_fr,
        c.name_ar as category_name_ar,
        bl.name as location_name,
        bl.address, bl.city, bl.latitude, bl.longitude,
        pi.image_url as primary_image,
        ${req.user ? `(SELECT COUNT(*) FROM user_favorites WHERE user_id = '${req.user.id}' AND promotion_id = p.id) > 0 as is_favorite` : 'false as is_favorite'}
        ${distanceSelect}
      FROM promotions p
      LEFT JOIN merchant_profiles mp ON p.merchant_id = mp.id
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN business_locations bl ON p.location_id = bl.id
      LEFT JOIN promotion_images pi ON p.id = pi.promotion_id AND pi.is_primary = true
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY ${distanceOrder || orderBy} ${order}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);

    const promotionsResult = await query(promotionsQuery, queryParams);

    // Get total count
    const countQuery = `
      SELECT COUNT(DISTINCT p.id)
      FROM promotions p
      LEFT JOIN business_locations bl ON p.location_id = bl.id
      WHERE ${whereConditions.join(' AND ')}
    `;

    const countResult = await query(countQuery, queryParams.slice(0, -2));
    const total = parseInt(countResult.rows[0].count);
    const pages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: {
        promotions: promotionsResult.rows,
        pagination: {
          page,
          limit,
          total,
          pages
        }
      }
    });
  } catch (error) {
    logger.error('Get promotions error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get promotion by ID
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;

    const promotionResult = await query(`
      SELECT 
        p.*, 
        mp.business_name as merchant_name,
        mp.logo_url as merchant_logo,
        mp.description as merchant_description,
        c.name as category_name,
        c.name_fr as category_name_fr,
        c.name_ar as category_name_ar,
        bl.name as location_name,
        bl.address, bl.city, bl.latitude, bl.longitude, bl.phone as location_phone,
        ${req.user ? `(SELECT COUNT(*) FROM user_favorites WHERE user_id = '${req.user.id}' AND promotion_id = p.id) > 0 as is_favorite` : 'false as is_favorite'}
      FROM promotions p
      LEFT JOIN merchant_profiles mp ON p.merchant_id = mp.id
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN business_locations bl ON p.location_id = bl.id
      WHERE p.id = $1
    `, [id]);

    if (promotionResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Promotion not found'
      });
    }

    // Get promotion images
    const imagesResult = await query(`
      SELECT id, image_url, alt_text, is_primary, sort_order
      FROM promotion_images
      WHERE promotion_id = $1
      ORDER BY sort_order, is_primary DESC
    `, [id]);

    const promotion = {
      ...promotionResult.rows[0],
      images: imagesResult.rows
    };

    res.json({
      success: true,
      data: { promotion }
    });
  } catch (error) {
    logger.error('Get promotion error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Create promotion (merchant only)
router.post('/', [
  authenticateToken,
  requireMerchant,
  body('title').trim().isLength({ min: 5, max: 255 }),
  body('description').trim().isLength({ min: 10, max: 2000 }),
  body('categoryId').isUUID(),
  body('locationId').optional().isUUID(),
  body('originalPrice').optional().isFloat({ min: 0 }),
  body('discountedPrice').optional().isFloat({ min: 0 }),
  body('discountPercentage').optional().isInt({ min: 1, max: 100 }),
  body('promotionType').isIn(['percentage', 'fixed_amount', 'buy_one_get_one', 'free_shipping']),
  body('startDate').isISO8601(),
  body('endDate').isISO8601(),
  body('termsConditions').optional().trim().isLength({ max: 1000 }),
  body('maxRedemptions').optional().isInt({ min: 1 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const {
      title, description, categoryId, locationId, originalPrice, discountedPrice,
      discountPercentage, promotionType, startDate, endDate, termsConditions,
      maxRedemptions, titleFr, titleAr, descriptionFr, descriptionAr
    } = req.body;

    // Get merchant profile
    const merchantResult = await query(
      'SELECT id FROM merchant_profiles WHERE user_id = $1',
      [req.user.id]
    );

    if (merchantResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Merchant profile not found'
      });
    }

    const merchantId = merchantResult.rows[0].id;

    // Calculate discount values
    let finalDiscountPercentage = discountPercentage;
    let discountAmount = null;

    if (originalPrice && discountedPrice) {
      finalDiscountPercentage = Math.round(((originalPrice - discountedPrice) / originalPrice) * 100);
      discountAmount = originalPrice - discountedPrice;
    }

    const promotionResult = await query(`
      INSERT INTO promotions (
        merchant_id, category_id, location_id, title, description,
        original_price, discounted_price, discount_percentage, discount_amount,
        promotion_type, start_date, end_date, terms_conditions, max_redemptions,
        title_fr, title_ar, description_fr, description_ar
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
      RETURNING *
    `, [
      merchantId, categoryId, locationId, title, description,
      originalPrice, discountedPrice, finalDiscountPercentage, discountAmount,
      promotionType, startDate, endDate, termsConditions, maxRedemptions,
      titleFr, titleAr, descriptionFr, descriptionAr
    ]);

    res.status(201).json({
      success: true,
      message: 'Promotion created successfully',
      data: { promotion: promotionResult.rows[0] }
    });
  } catch (error) {
    logger.error('Create promotion error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Update promotion (merchant only)
router.put('/:id', [
  authenticateToken,
  requireMerchant,
  body('title').optional().trim().isLength({ min: 5, max: 255 }),
  body('description').optional().trim().isLength({ min: 10, max: 2000 }),
  body('originalPrice').optional().isFloat({ min: 0 }),
  body('discountedPrice').optional().isFloat({ min: 0 }),
  body('startDate').optional().isISO8601(),
  body('endDate').optional().isISO8601()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const updateFields = req.body;

    // Check if promotion belongs to merchant
    const promotionResult = await query(`
      SELECT p.id FROM promotions p
      JOIN merchant_profiles mp ON p.merchant_id = mp.id
      WHERE p.id = $1 AND mp.user_id = $2
    `, [id, req.user.id]);

    if (promotionResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Promotion not found or access denied'
      });
    }

    // Build update query
    const updateKeys = Object.keys(updateFields);
    const setClause = updateKeys.map((key, index) => `${key} = $${index + 2}`).join(', ');
    const values = [id, ...Object.values(updateFields)];

    const updatedPromotion = await query(`
      UPDATE promotions 
      SET ${setClause}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `, values);

    res.json({
      success: true,
      message: 'Promotion updated successfully',
      data: { promotion: updatedPromotion.rows[0] }
    });
  } catch (error) {
    logger.error('Update promotion error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Delete promotion (merchant only)
router.delete('/:id', authenticateToken, requireMerchant, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if promotion belongs to merchant
    const result = await query(`
      DELETE FROM promotions 
      WHERE id = $1 AND merchant_id IN (
        SELECT id FROM merchant_profiles WHERE user_id = $2
      )
    `, [id, req.user.id]);

    if (result.rowCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'Promotion not found or access denied'
      });
    }

    res.json({
      success: true,
      message: 'Promotion deleted successfully'
    });
  } catch (error) {
    logger.error('Delete promotion error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
