const express = require('express');
const { body, validationResult } = require('express-validator');
const { query } = require('../database/connection');
const { authenticateToken, requireMerchant } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Get merchant locations
router.get('/', authenticateToken, requireMerchant, async (req, res) => {
  try {
    const merchantResult = await query(
      'SELECT id FROM merchant_profiles WHERE user_id = $1',
      [req.user.id]
    );

    if (merchantResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Merchant profile not found'
      });
    }

    const merchantId = merchantResult.rows[0].id;

    const locationsResult = await query(`
      SELECT *
      FROM business_locations
      WHERE merchant_id = $1 AND is_active = true
      ORDER BY is_primary DESC, name
    `, [merchantId]);

    res.json({
      success: true,
      data: { locations: locationsResult.rows }
    });
  } catch (error) {
    logger.error('Get locations error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Add new location
router.post('/', [
  authenticateToken,
  requireMerchant,
  body('name').trim().isLength({ min: 2, max: 255 }),
  body('address').trim().isLength({ min: 5, max: 500 }),
  body('city').trim().isLength({ min: 2, max: 100 }),
  body('country').trim().isLength({ min: 2, max: 100 }),
  body('latitude').isFloat({ min: -90, max: 90 }),
  body('longitude').isFloat({ min: -180, max: 180 }),
  body('phone').optional().isMobilePhone(),
  body('isPrimary').optional().isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const merchantResult = await query(
      'SELECT id FROM merchant_profiles WHERE user_id = $1',
      [req.user.id]
    );

    if (merchantResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Merchant profile not found'
      });
    }

    const merchantId = merchantResult.rows[0].id;
    const {
      name, address, city, state, country, postalCode,
      latitude, longitude, phone, isPrimary = false
    } = req.body;

    // If this is primary, unset other primary locations
    if (isPrimary) {
      await query(
        'UPDATE business_locations SET is_primary = false WHERE merchant_id = $1',
        [merchantId]
      );
    }

    const locationResult = await query(`
      INSERT INTO business_locations (
        merchant_id, name, address, city, state, country, postal_code,
        latitude, longitude, phone, is_primary
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *
    `, [
      merchantId, name, address, city, state, country, postalCode,
      latitude, longitude, phone, isPrimary
    ]);

    res.status(201).json({
      success: true,
      message: 'Location added successfully',
      data: { location: locationResult.rows[0] }
    });
  } catch (error) {
    logger.error('Add location error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
