version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: promotun-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-promotun}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-SecurePromoTunPass123!}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/01-init-db.sql
    ports:
      - "5432:5432"
    networks:
      - promotun-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d promotun"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: promotun-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-SecureRedisPass123!}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - promotun-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "SecureRedisPass123!", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Backend API
  backend:
    build:
      context: ../backend
      dockerfile: Dockerfile
    container_name: promotun-backend
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      PORT: 5000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${POSTGRES_DB:-promotun}
      DB_USER: ${POSTGRES_USER:-postgres}
      DB_PASSWORD: ${POSTGRES_PASSWORD:-SecurePromoTunPass123!}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-SecureRedisPass123!}
      JWT_SECRET: ${JWT_SECRET:-PromoTun-Super-Secret-JWT-Key-For-Production}
      BCRYPT_ROUNDS: ${BCRYPT_ROUNDS:-12}
      SESSION_SECRET: ${SESSION_SECRET:-PromoTun-Session-Secret-Key}
      LOG_LEVEL: ${LOG_LEVEL:-info}
      RATE_LIMIT_WINDOW_MS: ${RATE_LIMIT_WINDOW_MS:-900000}
      RATE_LIMIT_MAX_REQUESTS: ${RATE_LIMIT_MAX_REQUESTS:-100}
      FRONTEND_URL: ${FRONTEND_URL:-http://localhost}
      MOCK_DATABASE: false
      MOCK_EXTERNAL_APIS: false
    ports:
      - "5000:5000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - promotun-network
    restart: unless-stopped
    volumes:
      - backend_uploads:/app/uploads
      - backend_logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Simple Nginx for API access
  nginx:
    image: nginx:alpine
    container_name: promotun-nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx-minimal.conf:/etc/nginx/nginx.conf:ro
      - backend_uploads:/var/www/uploads:ro
    depends_on:
      - backend
    networks:
      - promotun-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  postgres_data:
  redis_data:
  backend_uploads:
  backend_logs:

networks:
  promotun-network:
    driver: bridge
