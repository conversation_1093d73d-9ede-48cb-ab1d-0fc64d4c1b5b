// Test setup file
require('dotenv').config({ path: '.env.test' });

// Mock external services for testing
jest.mock('../services/emailService', () => ({
  sendEmail: jest.fn().mockResolvedValue({ success: true, messageId: 'test-message-id' })
}));

jest.mock('../services/notificationService', () => ({
  sendPushNotification: jest.fn().mockResolvedValue({ success: true }),
  registerDeviceToken: jest.fn().mockResolvedValue({ success: true }),
  unregisterDeviceToken: jest.fn().mockResolvedValue({ success: true })
}));

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only';
process.env.DB_NAME = 'promotun_test';

// Global test timeout
jest.setTimeout(30000);

// Clean up after tests
afterAll(async () => {
  // Close database connections, etc.
  if (global.__MONGO_URI__) {
    await global.__MONGO_URI__.stop();
  }
});
