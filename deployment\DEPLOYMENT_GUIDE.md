# PromoTun Production Deployment Guide

## Infrastructure Overview

### Server Configuration
- **Application VM**: ************** (Ubuntu 22.04 LTS)
  - Docker containers for all application services
  - Nginx reverse proxy with SSL termination
  - Redis cache
  - Monitoring stack (Prometheus, Grafana)

- **Database VM**: ************** (Ubuntu 22.04 LTS)
  - PostgreSQL 15 (native installation)
  - Database: `promodetect`
  - Automated backups and monitoring

- **Domain**: promodetect.com
- **SSL**: Let's Encrypt certificates with auto-renewal

## Pre-Deployment Checklist

### DNS Configuration
- [ ] Domain `promodetect.com` points to **************
- [ ] WWW subdomain configured (optional)
- [ ] DNS propagation completed (check with `dig promodetect.com`)

### Network Security
- [ ] Firewall rules configured on both VMs
- [ ] SSH key-based authentication enabled
- [ ] Database VM accessible only from application VM
- [ ] SSL certificates ready or Let's Encrypt configured

### Environment Variables
- [ ] Database credentials configured
- [ ] JWT secrets generated (minimum 32 characters)
- [ ] External API keys configured (Firebase, SendGrid, Google Maps)
- [ ] Monitoring passwords set

## Step-by-Step Deployment

### 1. Database VM Setup (**************)

```bash
# Connect to database VM
ssh user@**************

# Download and run database setup script
wget https://raw.githubusercontent.com/your-repo/promotun/main/deployment/scripts/setup-database-vm.sh
chmod +x setup-database-vm.sh
./setup-database-vm.sh

# Note the database password from /root/db_credentials.txt
sudo cat /root/db_credentials.txt
```

**Expected Results:**
- PostgreSQL 15 installed and configured
- Database `promodetect` created with schema
- Remote access configured for application VM
- Firewall rules applied
- Automated backups configured
- Monitoring tools installed

### 2. Application VM Setup (**************)

```bash
# Connect to application VM
ssh user@**************

# Download and run application setup script
wget https://raw.githubusercontent.com/your-repo/promotun/main/deployment/scripts/setup-application-vm.sh
chmod +x setup-application-vm.sh
./setup-application-vm.sh

# Reboot to apply all changes
sudo reboot
```

**Expected Results:**
- Docker and Docker Compose installed
- Application user `promotun` created
- Firewall configured
- Security hardening applied
- SSL tools installed
- Monitoring tools configured

### 3. Application Code Deployment

```bash
# Switch to promotun user
sudo -u promotun -i

# Navigate to application directory
cd /opt/promotun

# Clone your repository (replace with your actual repository URL)
git clone https://github.com/your-repo/promotun.git .

# Copy production environment file
cp deployment/.env.production deployment/.env

# Copy nginx site configuration
cp deployment/nginx/sites-enabled/promodetect.com.template deployment/nginx/sites-enabled/promodetect.com

# Edit environment file with actual values
nano deployment/.env
```

**Required Environment Variables:**
```bash
# Database Configuration
DB_HOST=**************
DB_PASSWORD=your_actual_database_password

# Security Secrets (generate strong passwords)
JWT_SECRET=your_32_character_jwt_secret
SESSION_SECRET=your_32_character_session_secret
NEXTAUTH_SECRET=your_32_character_nextauth_secret
REDIS_PASSWORD=your_secure_redis_password

# External APIs
FIREBASE_PROJECT_ID=your_firebase_project
SENDGRID_API_KEY=your_sendgrid_key
GOOGLE_MAPS_API_KEY=your_google_maps_key

# Monitoring
GRAFANA_PASSWORD=your_secure_grafana_password
```

### 4. SSL Certificate Setup

```bash
# Run SSL setup script as root
sudo /opt/promotun/deployment/scripts/setup-ssl.sh
```

**Expected Results:**
- Let's Encrypt certificates obtained
- Certificates copied to application directory
- Auto-renewal configured
- Enhanced SSL configuration created

### 5. Application Deployment

```bash
# Deploy the application
cd /opt/promotun/deployment
./scripts/deploy-production.sh
```

**Expected Results:**
- Docker images built successfully
- All containers started and healthy
- Services accessible via HTTPS
- Monitoring dashboard available

## Post-Deployment Verification

### 1. Service Health Checks

```bash
# Check container status
docker-compose -f docker-compose.production.yml ps

# Check service health
curl -f https://promodetect.com/health
curl -f https://promodetect.com/api/health
```

### 2. Database Connectivity

```bash
# Test database connection from application VM
PGPASSWORD=your_db_password psql -h ************** -U postgres -d promodetect -c "SELECT version();"
```

### 3. SSL Certificate Verification

```bash
# Check SSL certificate
openssl s_client -connect promodetect.com:443 -servername promodetect.com

# Check SSL rating (external tool)
curl -s "https://api.ssllabs.com/api/v3/analyze?host=promodetect.com"
```

### 4. Performance Testing

```bash
# Basic load test
ab -n 100 -c 10 https://promodetect.com/

# API endpoint test
ab -n 50 -c 5 https://promodetect.com/api/categories
```

## Monitoring and Maintenance

### Access Monitoring Dashboard

1. **Grafana**: https://promodetect.com/grafana
   - Username: admin
   - Password: (from GRAFANA_PASSWORD in .env)

2. **Prometheus**: https://promodetect.com/prometheus
   - Basic auth protected
   - Metrics and alerting rules

### Log Management

```bash
# View application logs
docker-compose -f docker-compose.production.yml logs -f backend
docker-compose -f docker-compose.production.yml logs -f merchant-portal
docker-compose -f docker-compose.production.yml logs -f nginx

# System logs
sudo journalctl -u docker -f
sudo tail -f /var/log/nginx/access.log
```

### Backup Verification

```bash
# Manual backup
/opt/promotun/backup.sh

# Check backup files
ls -la /opt/promotun/backups/

# Database backup verification
ls -la /var/backups/postgresql/
```

### Performance Monitoring

Key metrics to monitor:
- **Response Time**: API endpoints < 500ms
- **Memory Usage**: Containers < 80% allocated memory
- **CPU Usage**: Average < 70%
- **Disk Space**: < 80% usage
- **Database Connections**: < 80% of max_connections

## Troubleshooting

### Common Issues

#### 1. Container Won't Start
```bash
# Check logs
docker-compose -f docker-compose.production.yml logs [service_name]

# Check resource usage
docker stats

# Restart specific service
docker-compose -f docker-compose.production.yml restart [service_name]
```

#### 2. Database Connection Issues
```bash
# Test from application VM
telnet ************** 5432

# Check PostgreSQL logs on database VM
sudo tail -f /var/log/postgresql/postgresql-15-main.log

# Verify firewall rules
sudo ufw status
```

#### 3. SSL Certificate Issues
```bash
# Check certificate validity
openssl x509 -in /opt/promotun/ssl/promodetect.com.crt -text -noout

# Manual renewal
sudo certbot renew --dry-run

# Check renewal logs
sudo tail -f /var/log/ssl-renewal.log
```

#### 4. High Memory Usage
```bash
# Check container memory usage
docker stats --no-stream

# Restart memory-intensive services
docker-compose -f docker-compose.production.yml restart backend merchant-portal

# Clear system cache
sudo sync && sudo sysctl vm.drop_caches=3
```

### Emergency Procedures

#### 1. Complete Service Restart
```bash
cd /opt/promotun/deployment
docker-compose -f docker-compose.production.yml down
docker-compose -f docker-compose.production.yml up -d
```

#### 2. Rollback Deployment
```bash
# Stop current deployment
docker-compose -f docker-compose.production.yml down

# Restore from backup
cd /opt/promotun/backups
# Restore volumes from latest backup
docker run --rm -v promotun_backend_uploads:/data -v $(pwd):/backup alpine tar xzf /backup/latest_uploads.tar.gz -C /data

# Restart with previous configuration
docker-compose -f docker-compose.production.yml up -d
```

#### 3. Database Recovery
```bash
# On database VM
sudo -u postgres psql

# Restore from backup
sudo -u postgres pg_restore -d promodetect /var/backups/postgresql/latest_backup.sql
```

## Maintenance Schedule

### Daily
- [ ] Check service health via monitoring dashboard
- [ ] Review error logs
- [ ] Verify backup completion

### Weekly
- [ ] Update system packages
- [ ] Review security logs
- [ ] Performance analysis
- [ ] Disk space cleanup

### Monthly
- [ ] Security audit
- [ ] SSL certificate check
- [ ] Database optimization
- [ ] Backup restoration test

## Security Considerations

### Network Security
- Firewall rules restrict database access to application VM only
- All external traffic goes through HTTPS
- Internal container communication on isolated network

### Application Security
- JWT tokens with secure secrets
- Rate limiting on API endpoints
- Input validation and sanitization
- CORS properly configured

### Data Security
- Database connections use SSL
- Sensitive data encrypted at rest
- Regular security updates applied
- Access logs monitored

## Support and Escalation

### Log Locations
- Application logs: `/opt/promotun/logs/`
- Nginx logs: `/var/log/nginx/`
- System logs: `/var/log/syslog`
- Database logs: `/var/log/postgresql/`

### Key Configuration Files
- Docker Compose: `/opt/promotun/deployment/docker-compose.production.yml`
- Environment: `/opt/promotun/deployment/.env`
- Nginx: `/opt/promotun/deployment/nginx/nginx.production.conf`
- SSL: `/opt/promotun/ssl/`

### Emergency Contacts
- System Administrator: [contact info]
- Database Administrator: [contact info]
- Development Team: [contact info]
- Hosting Provider: [contact info]
