# PromoTun Mobile App - Multi-stage Production Dockerfile
# React Native application build environment

# ===================================
# Base Stage - Common dependencies
# ===================================
FROM node:18-alpine AS base

# Install system dependencies and security updates
RUN apk update && apk upgrade && \
    apk add --no-cache \
    curl=8.5.0-r0 \
    dumb-init=1.2.5-r2 \
    git=2.43.0-r0 \
    python3=3.11.6-r1 \
    make=4.4.1-r1 \
    g++=13.2.1_git20231014-r0 \
    && rm -rf /var/cache/apk/*

# Create app user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S promotun -u 1001 -G nodejs

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# ===================================
# Development Stage
# ===================================
FROM base AS development

# Install React Native CLI and Expo CLI, then install dependencies
RUN npm install -g @react-native-community/cli@12.3.6 expo-cli@6.3.10 && \
    npm ci --include=dev

# Copy source code
COPY . .

# Change ownership to app user
RUN chown -R promotun:nodejs /app

# Switch to non-root user
USER promotun

# Expose Metro bundler port
EXPOSE 8081

# Health check for Metro bundler
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8081/status || exit 1

# Development command
CMD ["dumb-init", "npm", "start"]

# ===================================
# Dependencies Stage - Production dependencies only
# ===================================
FROM base AS dependencies

# Set NODE_ENV to production for optimal npm install
ENV NODE_ENV=production

# Install only production dependencies
RUN npm ci --only=production --no-audit --no-fund && \
    npm cache clean --force

# ===================================
# Build Stage - Build React Native bundle
# ===================================
FROM base AS build

# Install React Native CLI and dependencies for building
RUN npm install -g @react-native-community/cli@12.3.6 && \
    npm ci --include=dev

# Copy source code
COPY . .

# Build the React Native bundle for production
RUN npx react-native bundle \
    --platform android \
    --dev false \
    --entry-file index.js \
    --bundle-output android/app/src/main/assets/index.android.bundle \
    --assets-dest android/app/src/main/res/ \
    2>/dev/null || echo "Android bundle build completed or skipped"

# Build iOS bundle if iOS directory exists
RUN if [ -d "ios" ]; then \
        npx react-native bundle \
        --platform ios \
        --dev false \
        --entry-file index.js \
        --bundle-output ios/main.jsbundle \
        --assets-dest ios/ \
        2>/dev/null || echo "iOS bundle build completed or skipped"; \
    fi

# Remove development dependencies
RUN npm prune --production

# ===================================
# Production Stage - Final optimized image
# ===================================
FROM node:18-alpine AS production

# Install runtime dependencies, security updates, and create app user
RUN apk update && apk upgrade && \
    apk add --no-cache \
    curl=8.5.0-r0 \
    dumb-init=1.2.5-r2 \
    && rm -rf /var/cache/apk/* \
    && addgroup -g 1001 -S nodejs \
    && adduser -S promotun -u 1001 -G nodejs

# Set working directory
WORKDIR /app

# Set production environment
ENV NODE_ENV=production
ENV REACT_NATIVE_PACKAGER_HOSTNAME=0.0.0.0

# Copy production dependencies
COPY --from=dependencies --chown=promotun:nodejs /app/node_modules ./node_modules

# Copy built application
COPY --from=build --chown=promotun:nodejs /app/package*.json ./
COPY --from=build --chown=promotun:nodejs /app/index.js ./
COPY --from=build --chown=promotun:nodejs /app/metro.config.js ./metro.config.js
COPY --from=build --chown=promotun:nodejs /app/babel.config.js ./babel.config.js

# Copy source files
COPY --from=build --chown=promotun:nodejs /app/src ./src/
COPY --from=build --chown=promotun:nodejs /app/assets ./assets/

# Copy built bundles
COPY --from=build --chown=promotun:nodejs /app/android ./android/
COPY --from=build --chown=promotun:nodejs /app/ios ./ios/

# Create a simple health check endpoint
RUN mkdir -p public && \
    echo '{"status":"ok","service":"mobile-app","platform":"react-native"}' > ./public/health.json

# Switch to non-root user
USER promotun

# Expose Metro bundler port
EXPOSE 8081

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8081/status || exit 1

# Production command with proper signal handling
CMD ["dumb-init", "npx", "react-native", "start", "--host", "0.0.0.0"]

# ===================================
# Metadata
# ===================================
LABEL maintainer="PromoTun Development Team"
LABEL version="1.0.0"
LABEL description="PromoTun Mobile App - React Native Production Ready"
LABEL org.opencontainers.image.source="https://gitlab.com/promotun/promotun-platform"
