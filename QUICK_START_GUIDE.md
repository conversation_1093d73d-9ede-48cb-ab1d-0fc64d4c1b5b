# PromoTun Quick Start Guide

## 🚀 Getting Started

Welcome to PromoTun! This guide will help you get the application running in your local development environment in just a few minutes.

## ✅ Prerequisites

- **Node.js** 18+ installed
- **npm** or **yarn** package manager
- **Git** for version control
- **Code Editor** (VS Code recommended)

## 🔧 Quick Setup (5 Minutes)

### 1. Clone and Setup
```bash
# Clone the repository
git clone https://github.com/your-org/promotun.git
cd promotun

# Install backend dependencies
cd backend
npm install

# Start the backend server
npm start
```

### 2. Verify Installation
```bash
# Test the API (in a new terminal)
node test-runner.js

# Run integration tests
node integration-test.js
```

### 3. Access the Application
- **Backend API:** http://localhost:5000
- **Health Check:** http://localhost:5000/health
- **API Testing:** Use the provided test scripts

## 📱 Mobile App Development

### Setup React Native
```bash
# Navigate to mobile app directory
cd mobile-app

# Install dependencies (when ready)
npm install

# Start Expo development server
npx expo start
```

### Development Tools
- **Expo CLI:** For React Native development
- **Android Studio:** For Android development
- **Xcode:** For iOS development (macOS only)

## 🏪 Merchant Portal Development

### Setup Next.js Portal
```bash
# Navigate to merchant portal
cd merchant-portal

# Install dependencies (when ready)
npm install

# Start development server
npm run dev
```

Access at: http://localhost:3000

## 🗄️ Database Setup (Optional)

### For Full Functionality
```bash
# Install PostgreSQL
# Create database
createdb promotun

# Run migrations
cd backend
npm run migrate

# Seed initial data
npm run seed
```

### Redis Setup (Optional)
```bash
# Install Redis
# Start Redis server
redis-server

# Update .env file with Redis connection
```

## 🔧 Environment Configuration

### Backend (.env)
```env
NODE_ENV=development
PORT=5000
DB_HOST=localhost
DB_NAME=promotun
JWT_SECRET=your-secret-key
MOCK_DATABASE=true
MOCK_EXTERNAL_APIS=true
```

### Mobile App
```env
EXPO_PUBLIC_API_URL=http://localhost:5000/api
```

### Merchant Portal
```env
NEXT_PUBLIC_API_URL=http://localhost:5000/api
```

## 🧪 Testing

### Run All Tests
```bash
# Comprehensive testing
node test-runner.js

# Integration testing
node integration-test.js

# API endpoint testing
node test-api.js
```

### Backend Unit Tests
```bash
cd backend
npm test
```

## 📚 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout

### Users
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update profile
- `GET /api/users/favorites` - Get favorites

### Promotions
- `GET /api/promotions` - List promotions
- `GET /api/promotions/:id` - Get promotion details
- `POST /api/promotions` - Create promotion (merchant)

### Categories
- `GET /api/categories` - List categories

## 🔑 Test Credentials

### Consumer Account
```
Email: <EMAIL>
Password: TestPass123!
```

### Merchant Account
```
Email: <EMAIL>
Password: TestPass123!
```

### Admin Account
```
Email: <EMAIL>
Password: AdminPass123!
```

## 🌍 Multi-language Testing

### Switch Languages
```javascript
// In mobile app
i18n.changeLanguage('fr'); // French
i18n.changeLanguage('ar'); // Arabic
i18n.changeLanguage('en'); // English
```

### Test RTL Support
```javascript
// Check RTL functionality
import { isRTL } from './src/utils/rtlUtils';
console.log(isRTL('ar')); // true
```

## 🔄 Real-time Features

### WebSocket Connection
```javascript
// Connect to WebSocket
const socket = io('http://localhost:5000');

// Join location-based room
socket.emit('join_location', { lat: 40.7128, lng: -74.0060 });

// Listen for new promotions
socket.on('new_promotion', (promotion) => {
  console.log('New promotion:', promotion);
});
```

## 🛠️ Development Tools

### Recommended VS Code Extensions
- ES7+ React/Redux/React-Native snippets
- Prettier - Code formatter
- ESLint
- Thunder Client (API testing)
- GitLens

### Debugging
```bash
# Backend debugging
DEBUG=* npm start

# View logs
tail -f backend/logs/combined.log
```

## 📊 Monitoring

### Health Check
```bash
curl http://localhost:5000/health
```

### Performance Monitoring
- Check response times in logs
- Monitor memory usage
- Track API endpoint performance

## 🚨 Troubleshooting

### Common Issues

#### Server Won't Start
```bash
# Check if port is in use
netstat -an | findstr :5000

# Kill process if needed
taskkill /F /PID <process-id>
```

#### Database Connection Issues
```bash
# Verify PostgreSQL is running
pg_isready

# Check connection string in .env
```

#### Module Not Found
```bash
# Clear npm cache
npm cache clean --force

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

## 📖 Documentation

- **API Documentation:** `docs/API_DOCUMENTATION.md`
- **Deployment Guide:** `docs/DEPLOYMENT_GUIDE.md`
- **Architecture Overview:** `docs/ARCHITECTURE.md`
- **Testing Report:** `TESTING_DEPLOYMENT_REPORT.md`

## 🎯 Next Steps

1. **Set up real database** (PostgreSQL)
2. **Configure Firebase** for push notifications
3. **Start mobile app development**
4. **Build merchant portal features**
5. **Implement real-time features**
6. **Add comprehensive testing**
7. **Deploy to staging environment**

## 🆘 Getting Help

### Resources
- **GitHub Issues:** Report bugs and feature requests
- **Documentation:** Comprehensive guides available
- **API Testing:** Use provided test scripts
- **Logs:** Check `backend/logs/` for debugging

### Support
- Check the troubleshooting section
- Review the testing report for known issues
- Use the provided test scripts to verify functionality

## 🎉 You're Ready!

PromoTun is now running in your local development environment. Start building amazing features!

**Happy coding! 🚀**

---

*Last updated: June 20, 2025*
