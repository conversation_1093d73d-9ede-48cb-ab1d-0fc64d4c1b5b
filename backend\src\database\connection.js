const { Pool } = require('pg');
const logger = require('../utils/logger');

let pool;

// Mock database for development
function createMockPool() {
  return {
    connect: async () => ({
      query: async (text, params) => {
        logger.debug(`Mock query: ${text.substring(0, 100)}...`);

        // Mock responses for common queries
        if (text.includes('SELECT NOW()')) {
          return { rows: [{ now: new Date() }] };
        }

        if (text.includes('SELECT table_name FROM information_schema.tables')) {
          return { rows: [{ table_name: 'users' }, { table_name: 'promotions' }] };
        }

        if (text.includes('INSERT INTO users')) {
          return {
            rows: [{
              id: 'mock-user-id',
              email: params?.[0] || '<EMAIL>',
              first_name: params?.[2] || 'Mock',
              last_name: params?.[3] || 'User',
              user_type: params?.[4] || 'consumer',
              preferred_language: params?.[5] || 'en',
              is_verified: false,
              created_at: new Date()
            }],
            rowCount: 1
          };
        }

        if (text.includes('SELECT') && text.includes('FROM users WHERE email')) {
          // Check if this is a registration check (should return empty for new users)
          if (params && params[0] === '<EMAIL>') {
            return { rows: [], rowCount: 0 }; // Allow registration
          }

          return {
            rows: [{
              id: 'mock-user-id',
              email: '<EMAIL>',
              password_hash: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Dh/Ey2', // "password"
              first_name: 'Mock',
              last_name: 'User',
              user_type: 'consumer',
              preferred_language: 'en',
              is_verified: true,
              is_active: true
            }],
            rowCount: 1
          };
        }

        if (text.includes('SELECT') && text.includes('FROM promotions')) {
          return {
            rows: [
              {
                id: 'mock-promotion-1',
                title: 'Mock Promotion 1',
                description: 'This is a mock promotion for testing',
                original_price: 100.00,
                discounted_price: 50.00,
                discount_percentage: 50,
                start_date: new Date(),
                end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
                merchant_name: 'Mock Merchant',
                category_name: 'Electronics',
                is_favorite: false
              }
            ],
            rowCount: 1
          };
        }

        if (text.includes('SELECT') && text.includes('FROM categories')) {
          return {
            rows: [
              { id: 'cat-1', name: 'Electronics', name_fr: 'Électronique', name_ar: 'إلكترونيات' },
              { id: 'cat-2', name: 'Fashion', name_fr: 'Mode', name_ar: 'أزياء' },
              { id: 'cat-3', name: 'Food', name_fr: 'Alimentation', name_ar: 'طعام' }
            ],
            rowCount: 3
          };
        }

        if (text.includes('COUNT(*)')) {
          return { rows: [{ count: '10' }], rowCount: 1 };
        }

        // Default mock response
        return { rows: [], rowCount: 0 };
      },
      release: () => {}
    }),
    query: async (text, params) => {
      const client = await this.connect();
      return client.query(text, params);
    },
    end: async () => {
      logger.info('Mock database connection closed');
    }
  };
}

const dbConfig = {
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'l192.168.100.14',
  database: process.env.DB_NAME || 'promotun',
  password: process.env.DB_PASSWORD || 'Tradigi**2023',
  port: process.env.DB_PORT || 5432,
  max: 20, // maximum number of clients in the pool
  idleTimeoutMillis: 30000, // how long a client is allowed to remain idle
  connectionTimeoutMillis: 2000, // how long to wait when connecting a new client
};

async function connectDatabase() {
  try {
    // In development mode without PostgreSQL, use mock connection
    if (process.env.MOCK_DATABASE === 'true') {
      logger.warn('Using mock database connection for development');
      pool = createMockPool();
      return pool;
    }

    pool = new Pool(dbConfig);

    // Test the connection with retries for Docker startup
    let retries = 5;
    while (retries > 0) {
      try {
        const client = await pool.connect();
        await client.query('SELECT NOW()');
        client.release();
        logger.info('Database connection established successfully');
        return pool;
      } catch (error) {
        retries--;
        if (retries === 0) throw error;
        logger.warn(`Database connection failed, retrying... (${retries} attempts left)`);
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
  } catch (error) {
    logger.error('Database connection failed:', error);
    throw error;
  }
}

function getPool() {
  if (!pool) {
    throw new Error('Database not connected. Call connectDatabase() first.');
  }
  return pool;
}

async function query(text, params) {
  const client = await pool.connect();
  try {
    const start = Date.now();
    const result = await client.query(text, params);
    const duration = Date.now() - start;
    
    logger.debug('Executed query', {
      text: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
      duration,
      rows: result.rowCount
    });
    
    return result;
  } catch (error) {
    logger.error('Database query error:', error);
    throw error;
  } finally {
    client.release();
  }
}

async function transaction(callback) {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    logger.error('Transaction error:', error);
    throw error;
  } finally {
    client.release();
  }
}

async function closeDatabase() {
  if (pool) {
    await pool.end();
    logger.info('Database connection closed');
  }
}

module.exports = {
  connectDatabase,
  getPool,
  query,
  transaction,
  closeDatabase
};
