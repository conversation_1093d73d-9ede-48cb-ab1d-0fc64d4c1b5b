const express = require('express');
const { query } = require('../database/connection');
const logger = require('../utils/logger');

const router = express.Router();

// Get all categories
router.get('/', async (req, res) => {
  try {
    const categoriesResult = await query(`
      SELECT 
        id, name, name_fr, name_ar, description, description_fr, description_ar,
        parent_id, icon_url, sort_order, is_active
      FROM categories
      WHERE is_active = true
      ORDER BY sort_order, name
    `);

    // Build category tree
    const categories = categoriesResult.rows;
    const categoryMap = {};
    const rootCategories = [];

    // Create category map
    categories.forEach(category => {
      categoryMap[category.id] = {
        ...category,
        children: []
      };
    });

    // Build tree structure
    categories.forEach(category => {
      if (category.parent_id) {
        if (categoryMap[category.parent_id]) {
          categoryMap[category.parent_id].children.push(categoryMap[category.id]);
        }
      } else {
        rootCategories.push(categoryMap[category.id]);
      }
    });

    res.json({
      success: true,
      data: { categories: rootCategories }
    });
  } catch (error) {
    logger.error('Get categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get category by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const categoryResult = await query(`
      SELECT 
        id, name, name_fr, name_ar, description, description_fr, description_ar,
        parent_id, icon_url, sort_order, is_active
      FROM categories
      WHERE id = $1 AND is_active = true
    `, [id]);

    if (categoryResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }

    res.json({
      success: true,
      data: { category: categoryResult.rows[0] }
    });
  } catch (error) {
    logger.error('Get category error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
