const crypto = require('crypto');
const jwt = require('jsonwebtoken');

/**
 * Generate a random verification token
 */
function generateVerificationToken() {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Generate a random reset token
 */
function generateResetToken() {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Generate JWT token
 */
function generateJWTToken(payload, expiresIn = '7d') {
  return jwt.sign(payload, process.env.JWT_SECRET, { expiresIn });
}

/**
 * Verify JWT token
 */
function verifyJWTToken(token) {
  return jwt.verify(token, process.env.JWT_SECRET);
}

/**
 * Generate API key
 */
function generateApiKey() {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Hash a token for storage
 */
function hashToken(token) {
  return crypto.createHash('sha256').update(token).digest('hex');
}

/**
 * Generate secure random string
 */
function generateSecureRandom(length = 32) {
  return crypto.randomBytes(length).toString('hex');
}

module.exports = {
  generateVerificationToken,
  generateResetToken,
  generateJWTToken,
  verifyJWTToken,
  generateApiKey,
  hashToken,
  generateSecureRandom
};
