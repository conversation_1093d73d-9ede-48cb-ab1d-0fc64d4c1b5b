# PromoTun Prometheus Configuration

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'promotun-production'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets: []

# Load rules once and periodically evaluate them
rule_files:
  - "alert_rules.yml"

# Scrape configurations
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # Application VM Node Exporter
  - job_name: 'app-vm-node'
    static_configs:
      - targets: ['**************:9100']
    scrape_interval: 30s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'app-vm'

  # Database VM Node Exporter
  - job_name: 'db-vm-node'
    static_configs:
      - targets: ['**************:9100']
    scrape_interval: 30s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'db-vm'

  # Backend API Metrics
  - job_name: 'backend-api'
    static_configs:
      - targets: ['backend:5000']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s

  # Redis Metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
    metrics_path: /metrics

  # Nginx Metrics (if nginx-prometheus-exporter is configured)
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:9113']
    scrape_interval: 30s
    metrics_path: /metrics

  # Docker Container Metrics (cAdvisor)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    metrics_path: /metrics

  # Grafana Metrics
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    scrape_interval: 60s
    metrics_path: /metrics

# Remote write configuration (for external monitoring services)
# remote_write:
#   - url: "https://your-remote-prometheus-endpoint/api/v1/write"
#     basic_auth:
#       username: "your-username"
#       password: "your-password"
