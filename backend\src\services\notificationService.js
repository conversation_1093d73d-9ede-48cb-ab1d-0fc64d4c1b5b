const admin = require('firebase-admin');
const { query, transaction } = require('../database/connection');
const { setCache, getCache } = require('../config/redis');
const logger = require('../utils/logger');

// Initialize Firebase Admin SDK
let messaging = null;

try {
  if (!admin.apps.length && process.env.FIREBASE_PROJECT_ID && process.env.FIREBASE_CLIENT_EMAIL && process.env.FIREBASE_PRIVATE_KEY) {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
      }),
    });
    messaging = admin.messaging();
    logger.info('Firebase Admin SDK initialized successfully');
  } else {
    logger.warn('Firebase credentials not configured, push notifications will be mocked');
  }
} catch (error) {
  logger.error('Failed to initialize Firebase Admin SDK:', error.message);
  logger.warn('Push notifications will be mocked in development mode');
}

// messaging is initialized above conditionally

/**
 * Send push notification to a single device
 */
async function sendPushNotification(deviceToken, notification, data = {}) {
  try {
    // Mock notification in development if Firebase is not configured
    if (!messaging) {
      logger.info(`Mock push notification sent to ${deviceToken}: ${notification.title}`);
      return { success: true, messageId: 'mock-message-id' };
    }

    const message = {
      token: deviceToken,
      notification: {
        title: notification.title,
        body: notification.body,
        imageUrl: notification.imageUrl,
      },
      data: {
        ...data,
        notificationId: data.notificationId?.toString() || '',
        type: data.type || 'general',
      },
      android: {
        notification: {
          icon: 'ic_notification',
          color: '#FF6B35',
          sound: 'default',
          channelId: 'promotun_notifications',
        },
        priority: 'high',
      },
      apns: {
        payload: {
          aps: {
            sound: 'default',
            badge: 1,
          },
        },
      },
    };

    const response = await messaging.send(message);
    logger.info(`Push notification sent successfully: ${response}`);
    return { success: true, messageId: response };
  } catch (error) {
    logger.error('Error sending push notification:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Send push notification to multiple devices
 */
async function sendMulticastNotification(deviceTokens, notification, data = {}) {
  try {
    // Mock notification in development if Firebase is not configured
    if (!messaging) {
      logger.info(`Mock multicast notification sent to ${deviceTokens.length} devices: ${notification.title}`);
      return { success: true, successCount: deviceTokens.length, failureCount: 0 };
    }

    const message = {
      tokens: deviceTokens,
      notification: {
        title: notification.title,
        body: notification.body,
        imageUrl: notification.imageUrl,
      },
      data: {
        ...data,
        notificationId: data.notificationId?.toString() || '',
        type: data.type || 'general',
      },
      android: {
        notification: {
          icon: 'ic_notification',
          color: '#FF6B35',
          sound: 'default',
          channelId: 'promotun_notifications',
        },
        priority: 'high',
      },
      apns: {
        payload: {
          aps: {
            sound: 'default',
            badge: 1,
          },
        },
      },
    };

    const response = await messaging.sendMulticast(message);
    logger.info(`Multicast notification sent: ${response.successCount} successful, ${response.failureCount} failed`);
    
    // Handle failed tokens
    if (response.failureCount > 0) {
      const failedTokens = [];
      response.responses.forEach((resp, idx) => {
        if (!resp.success) {
          failedTokens.push(deviceTokens[idx]);
          logger.error(`Failed to send to token ${deviceTokens[idx]}: ${resp.error}`);
        }
      });
      
      // Remove invalid tokens from database
      await removeInvalidTokens(failedTokens);
    }

    return { 
      success: true, 
      successCount: response.successCount, 
      failureCount: response.failureCount 
    };
  } catch (error) {
    logger.error('Error sending multicast notification:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Create notification in database
 */
async function createNotification(userId, notificationData) {
  try {
    const { title, message, type, relatedId, titleFr, titleAr, messageFr, messageAr } = notificationData;
    
    const result = await query(
      `INSERT INTO notifications (user_id, title, message, notification_type, related_id, title_fr, title_ar, message_fr, message_ar)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) RETURNING *`,
      [userId, title, message, type, relatedId, titleFr, titleAr, messageFr, messageAr]
    );

    return result.rows[0];
  } catch (error) {
    logger.error('Error creating notification:', error);
    throw error;
  }
}

/**
 * Send notification to user
 */
async function sendNotificationToUser(userId, notificationData, pushData = {}) {
  try {
    // Create notification in database
    const notification = await createNotification(userId, notificationData);

    // Get user's device tokens and preferences
    const userResult = await query(
      `SELECT up.notification_preferences, dt.device_token, u.preferred_language
       FROM users u
       LEFT JOIN user_profiles up ON u.id = up.user_id
       LEFT JOIN device_tokens dt ON u.id = dt.user_id
       WHERE u.id = $1 AND u.is_active = true AND dt.is_active = true`,
      [userId]
    );

    if (userResult.rows.length === 0) {
      logger.warn(`No active devices found for user: ${userId}`);
      return { success: true, notification };
    }

    const user = userResult.rows[0];
    const preferences = user.notification_preferences || { push: true };
    
    // Check if user wants push notifications
    if (!preferences.push) {
      logger.info(`User ${userId} has disabled push notifications`);
      return { success: true, notification };
    }

    // Get localized notification content
    const localizedNotification = getLocalizedNotification(notification, user.preferred_language);
    
    // Send push notifications to all user devices
    const deviceTokens = userResult.rows.map(row => row.device_token).filter(Boolean);
    
    if (deviceTokens.length > 0) {
      const pushResult = await sendMulticastNotification(
        deviceTokens,
        localizedNotification,
        {
          ...pushData,
          notificationId: notification.id,
          type: notification.notification_type,
        }
      );

      logger.info(`Notification sent to user ${userId}: ${pushResult.successCount} devices`);
    }

    return { success: true, notification };
  } catch (error) {
    logger.error('Error sending notification to user:', error);
    throw error;
  }
}

/**
 * Send notification to multiple users
 */
async function sendNotificationToUsers(userIds, notificationData, pushData = {}) {
  try {
    const results = await Promise.allSettled(
      userIds.map(userId => sendNotificationToUser(userId, notificationData, pushData))
    );

    const successful = results.filter(result => result.status === 'fulfilled').length;
    const failed = results.filter(result => result.status === 'rejected').length;

    logger.info(`Bulk notification sent: ${successful} successful, ${failed} failed`);
    
    return { success: true, successful, failed };
  } catch (error) {
    logger.error('Error sending bulk notifications:', error);
    throw error;
  }
}

/**
 * Send location-based notifications
 */
async function sendLocationBasedNotification(latitude, longitude, radius, notificationData, pushData = {}) {
  try {
    // Get users within the specified radius
    const usersResult = await query(
      `SELECT DISTINCT u.id
       FROM users u
       JOIN user_profiles up ON u.id = up.user_id
       WHERE u.is_active = true 
       AND up.location_lat IS NOT NULL 
       AND up.location_lng IS NOT NULL
       AND (
         6371 * acos(
           cos(radians($1)) * cos(radians(up.location_lat)) * 
           cos(radians(up.location_lng) - radians($2)) + 
           sin(radians($1)) * sin(radians(up.location_lat))
         )
       ) <= $3`,
      [latitude, longitude, radius]
    );

    const userIds = usersResult.rows.map(row => row.id);
    
    if (userIds.length === 0) {
      logger.info('No users found in the specified location');
      return { success: true, userCount: 0 };
    }

    const result = await sendNotificationToUsers(userIds, notificationData, pushData);
    
    return { ...result, userCount: userIds.length };
  } catch (error) {
    logger.error('Error sending location-based notification:', error);
    throw error;
  }
}

/**
 * Get localized notification content
 */
function getLocalizedNotification(notification, language = 'en') {
  const titleMap = {
    en: notification.title,
    fr: notification.title_fr || notification.title,
    ar: notification.title_ar || notification.title,
  };

  const messageMap = {
    en: notification.message,
    fr: notification.message_fr || notification.message,
    ar: notification.message_ar || notification.message,
  };

  return {
    title: titleMap[language] || notification.title,
    body: messageMap[language] || notification.message,
  };
}

/**
 * Remove invalid device tokens
 */
async function removeInvalidTokens(tokens) {
  try {
    if (tokens.length === 0) return;

    await query(
      'UPDATE device_tokens SET is_active = false WHERE device_token = ANY($1)',
      [tokens]
    );

    logger.info(`Removed ${tokens.length} invalid device tokens`);
  } catch (error) {
    logger.error('Error removing invalid tokens:', error);
  }
}

/**
 * Register device token
 */
async function registerDeviceToken(userId, deviceToken, platform) {
  try {
    await query(
      `INSERT INTO device_tokens (user_id, device_token, platform, is_active)
       VALUES ($1, $2, $3, true)
       ON CONFLICT (device_token) 
       DO UPDATE SET user_id = $1, platform = $3, is_active = true, updated_at = CURRENT_TIMESTAMP`,
      [userId, deviceToken, platform]
    );

    logger.info(`Device token registered for user ${userId}`);
    return { success: true };
  } catch (error) {
    logger.error('Error registering device token:', error);
    throw error;
  }
}

/**
 * Unregister device token
 */
async function unregisterDeviceToken(deviceToken) {
  try {
    await query(
      'UPDATE device_tokens SET is_active = false WHERE device_token = $1',
      [deviceToken]
    );

    logger.info(`Device token unregistered: ${deviceToken}`);
    return { success: true };
  } catch (error) {
    logger.error('Error unregistering device token:', error);
    throw error;
  }
}

module.exports = {
  sendPushNotification,
  sendMulticastNotification,
  createNotification,
  sendNotificationToUser,
  sendNotificationToUsers,
  sendLocationBasedNotification,
  registerDeviceToken,
  unregisterDeviceToken,
  removeInvalidTokens,
};
