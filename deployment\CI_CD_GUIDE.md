# PromoTun CI/CD Pipeline Guide

## Overview

The PromoTun project uses a comprehensive GitLab CI/CD pipeline for automated testing, building, and deployment to Ubuntu 22.04 VMs. This guide covers the complete CI/CD setup and usage.

## Pipeline Architecture

### Stages
1. **Validate** - Dockerfile and Docker Compose validation
2. **Test** - Unit tests, integration tests, and code coverage
3. **Build** - Multi-stage Docker image builds
4. **Security Scan** - Container vulnerability scanning
5. **Deploy Staging** - Automated staging deployment
6. **Deploy Production** - Manual production deployment
7. **Cleanup** - Registry cleanup and maintenance

### Infrastructure
- **Application VM**: ************** (Docker containers)
- **Database VM**: ************** (PostgreSQL)
- **Domain**: promodetect.com
- **Registry**: GitLab Container Registry

## Dockerfiles

### Multi-Stage Architecture

All Dockerfiles use multi-stage builds for optimization:

#### Backend (`backend/Dockerfile`)
```dockerfile
# Stages: base → development → dependencies → build → production
FROM node:18-alpine AS base
FROM base AS development    # Dev environment with all deps
FROM base AS dependencies   # Production deps only
FROM base AS build         # Build stage with TypeScript compilation
FROM node:18-alpine AS production  # Final optimized image
```

**Features:**
- Non-root user (`promotun:nodejs`)
- Health checks with curl
- Security updates and minimal attack surface
- Proper signal handling with dumb-init
- Production optimizations

#### Frontend (`merchant-portal/Dockerfile`, `admin-dashboard/Dockerfile`)
```dockerfile
# Stages: base → development → dependencies → build → production
FROM node:18-alpine AS base
FROM base AS development    # Dev environment with hot reload
FROM base AS dependencies   # Production deps only
FROM base AS build         # Next.js build stage
FROM node:18-alpine AS production  # Final optimized image
```

**Features:**
- Next.js optimizations
- Static asset handling
- Health check endpoints
- Non-root user security
- Minimal production image

#### Mobile App (`mobile-app/Dockerfile`)
```dockerfile
# Stages: base → development → dependencies → build → production
FROM node:18-alpine AS base
FROM base AS development    # React Native dev environment
FROM base AS dependencies   # Production deps only
FROM base AS build         # Bundle generation
FROM node:18-alpine AS production  # Metro bundler server
```

**Features:**
- React Native bundle generation
- Metro bundler for development
- Cross-platform build support
- Development and production modes

## GitLab CI/CD Configuration

### Variables

#### Global Variables
```yaml
variables:
  DOCKER_DRIVER: overlay2
  APP_VM_HOST: "**************"
  DB_VM_HOST: "**************"
  DOMAIN_NAME: "promodetect.com"
  REGISTRY_URL: "$CI_REGISTRY"
```

#### Image Variables
```yaml
  BACKEND_IMAGE: "$CI_REGISTRY_IMAGE/backend"
  MERCHANT_PORTAL_IMAGE: "$CI_REGISTRY_IMAGE/merchant-portal"
  ADMIN_DASHBOARD_IMAGE: "$CI_REGISTRY_IMAGE/admin-dashboard"
  MOBILE_APP_IMAGE: "$CI_REGISTRY_IMAGE/mobile-app"
```

### Pipeline Jobs

#### Validation Jobs
- **validate-dockerfiles**: Hadolint validation
- **validate-compose**: Docker Compose syntax validation

#### Test Jobs
- **test-backend**: Unit tests with PostgreSQL and Redis
- **test-frontend**: Frontend tests for both portals

#### Build Jobs
- **build-backend**: Multi-stage backend image build
- **build-merchant-portal**: Merchant portal image build
- **build-admin-dashboard**: Admin dashboard image build
- **build-mobile-app**: Mobile app image build

#### Security Jobs
- **security-scan**: Trivy vulnerability scanning

#### Deployment Jobs
- **deploy-staging**: Automated staging deployment
- **deploy-production**: Manual production deployment
- **rollback-production**: Emergency rollback capability

## Deployment Scripts

### Core Scripts

#### `deploy-production.sh`
```bash
./deployment/scripts/deploy-production.sh [COMMIT_SHA]
```
- Zero-downtime deployment
- Pre-deployment backup
- Health checks and verification
- Registry image pulling
- Service orchestration

#### `deploy-staging.sh`
```bash
./deployment/scripts/deploy-staging.sh [COMMIT_SHA]
```
- Staging environment deployment
- Isolated testing environment
- Port mapping for conflict avoidance
- Quick iteration and testing

#### `create-backup.sh`
```bash
./deployment/scripts/create-backup.sh [BACKUP_NAME]
```
- Comprehensive backup creation
- Docker volumes, configs, database
- Automated retention management
- Backup verification

#### `rollback-deployment.sh`
```bash
./deployment/scripts/rollback-deployment.sh [VERSION]
```
- Emergency rollback capability
- Backup restoration
- Service verification
- Multiple rollback strategies

#### `post-deployment-tests.sh`
```bash
./deployment/scripts/post-deployment-tests.sh
```
- Comprehensive deployment verification
- Health checks, performance tests
- Security validation
- Monitoring verification

## Environment Configuration

### GitLab CI/CD Variables

#### Required Variables
```bash
# SSH Access
PRODUCTION_SSH_PRIVATE_KEY    # SSH key for production VM
STAGING_SSH_PRIVATE_KEY       # SSH key for staging VM

# Registry Access
CI_REGISTRY_PASSWORD          # GitLab registry password
CI_REGISTRY_USER             # GitLab registry username

# Application Configuration
DB_PASSWORD                  # Production database password
JWT_SECRET                   # JWT signing secret
REDIS_PASSWORD              # Redis password
```

#### Optional Variables
```bash
# External APIs
FIREBASE_PROJECT_ID         # Firebase project ID
SENDGRID_API_KEY           # SendGrid API key
GOOGLE_MAPS_API_KEY        # Google Maps API key

# Monitoring
GRAFANA_PASSWORD           # Grafana admin password
```

### VM Environment Files

#### Production (`.env.production`)
```bash
# Database Configuration
DB_HOST=**************
DB_PASSWORD=secure-production-password

# Security
JWT_SECRET=production-jwt-secret-32-chars-min
SESSION_SECRET=production-session-secret

# External APIs
FIREBASE_PROJECT_ID=your-firebase-project
SENDGRID_API_KEY=your-sendgrid-key
```

## Deployment Workflows

### Development Workflow
1. **Feature Development**
   ```bash
   git checkout -b feature/new-feature
   # Develop and test locally
   git push origin feature/new-feature
   ```

2. **Merge Request**
   - Triggers validation and test jobs
   - Code review and approval
   - Merge to develop branch

3. **Staging Deployment**
   ```bash
   # Automatic on develop branch
   # Manual trigger available
   ```

4. **Production Deployment**
   ```bash
   # Manual trigger on main branch
   # Requires approval
   ```

### Hotfix Workflow
1. **Emergency Fix**
   ```bash
   git checkout -b hotfix/critical-fix main
   # Implement fix
   git push origin hotfix/critical-fix
   ```

2. **Fast-track Deployment**
   - Direct merge to main
   - Immediate production deployment
   - Post-deployment verification

### Rollback Workflow
1. **Identify Issue**
   - Monitoring alerts
   - User reports
   - Health check failures

2. **Execute Rollback**
   ```bash
   # Via GitLab CI/CD
   # Manual trigger with version selection
   ```

3. **Investigate and Fix**
   - Root cause analysis
   - Fix development
   - Re-deployment

## Monitoring and Alerting

### Pipeline Monitoring
- **Job Status**: Success/failure notifications
- **Performance**: Build time tracking
- **Security**: Vulnerability alerts
- **Deployment**: Success/failure notifications

### Application Monitoring
- **Health Checks**: Automated endpoint monitoring
- **Performance**: Response time tracking
- **Security**: SSL certificate monitoring
- **Infrastructure**: Resource usage tracking

## Security Considerations

### Container Security
- **Non-root users**: All containers run as non-root
- **Minimal images**: Alpine-based with security updates
- **Vulnerability scanning**: Trivy integration
- **Secret management**: GitLab CI/CD variables

### Deployment Security
- **SSH key authentication**: No password access
- **Network isolation**: Firewall rules
- **SSL/TLS**: End-to-end encryption
- **Access control**: Role-based permissions

### Registry Security
- **Private registry**: GitLab Container Registry
- **Image signing**: Optional image verification
- **Access tokens**: Limited scope tokens
- **Cleanup policies**: Automated old image removal

## Troubleshooting

### Common Issues

#### Build Failures
```bash
# Check Dockerfile syntax
hadolint backend/Dockerfile

# Test local build
docker build -t test-image backend/

# Check dependencies
npm audit
```

#### Deployment Failures
```bash
# Check VM connectivity
ssh promotun@**************

# Check Docker status
docker ps
docker-compose logs

# Check disk space
df -h
```

#### Performance Issues
```bash
# Check resource usage
docker stats
htop

# Check logs
docker-compose logs -f backend
```

### Recovery Procedures

#### Failed Deployment
1. **Immediate Rollback**
   ```bash
   ./deployment/scripts/rollback-deployment.sh latest-stable
   ```

2. **Investigation**
   - Check pipeline logs
   - Review deployment logs
   - Analyze error messages

3. **Fix and Redeploy**
   - Implement fix
   - Test in staging
   - Deploy to production

#### Data Loss
1. **Stop Services**
   ```bash
   docker-compose down
   ```

2. **Restore from Backup**
   ```bash
   ./deployment/scripts/rollback-deployment.sh backup-YYYYMMDD_HHMMSS
   ```

3. **Verify Restoration**
   ```bash
   ./deployment/scripts/post-deployment-tests.sh
   ```

## Best Practices

### Development
- **Feature branches**: Use feature branches for development
- **Small commits**: Make atomic, focused commits
- **Testing**: Write and maintain comprehensive tests
- **Documentation**: Update documentation with changes

### Deployment
- **Staging first**: Always test in staging
- **Backup before deploy**: Automated backup creation
- **Monitor deployments**: Watch logs and metrics
- **Rollback plan**: Always have a rollback strategy

### Security
- **Secret rotation**: Regular secret updates
- **Access review**: Regular access audits
- **Vulnerability scanning**: Regular security scans
- **Incident response**: Documented response procedures

This CI/CD pipeline provides a robust, secure, and automated deployment process for the PromoTun platform, ensuring reliable delivery and operation in production environments.
