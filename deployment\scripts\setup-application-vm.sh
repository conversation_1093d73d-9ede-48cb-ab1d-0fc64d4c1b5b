#!/bin/bash

# PromoTun Application VM Setup Script
# Ubuntu 22.04 LTS - Application Server (**************)
# This script sets up Docker, security hardening, and application deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_VM_IP="**************"
DB_VM_IP="**************"
DOMAIN="promodetect.com"
APP_USER="promotun"
APP_DIR="/opt/promotun"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        print_error "This script should not be run as root. Please run as a regular user with sudo privileges."
        exit 1
    fi
}

# Update system packages
update_system() {
    print_info "Updating system packages..."
    sudo apt update && sudo apt upgrade -y
    sudo apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release
    print_status "System packages updated"
}

# Install Docker and Docker Compose
install_docker() {
    print_info "Installing Docker..."
    
    # Remove old Docker versions
    sudo apt remove -y docker docker-engine docker.io containerd runc 2>/dev/null || true
    
    # Add Docker's official GPG key
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # Add Docker repository
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # Install Docker
    sudo apt update
    sudo apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
    
    # Add user to docker group
    sudo usermod -aG docker $USER
    
    # Enable and start Docker
    sudo systemctl enable docker
    sudo systemctl start docker
    
    print_status "Docker installed successfully"
}

# Install Docker Compose standalone
install_docker_compose() {
    print_info "Installing Docker Compose..."
    
    # Get latest version
    DOCKER_COMPOSE_VERSION=$(curl -s https://api.github.com/repos/docker/compose/releases/latest | grep 'tag_name' | cut -d\" -f4)
    
    # Download and install
    sudo curl -L "https://github.com/docker/compose/releases/download/${DOCKER_COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    
    # Create symlink
    sudo ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
    
    print_status "Docker Compose installed successfully"
}

# Configure firewall
configure_firewall() {
    print_info "Configuring UFW firewall..."
    
    # Enable UFW
    sudo ufw --force enable
    
    # Default policies
    sudo ufw default deny incoming
    sudo ufw default allow outgoing
    
    # SSH access
    sudo ufw allow ssh
    sudo ufw allow 22/tcp
    
    # HTTP and HTTPS
    sudo ufw allow 80/tcp
    sudo ufw allow 443/tcp
    
    # Allow connection to database VM
    sudo ufw allow out to ${DB_VM_IP} port 5432
    
    # Docker daemon (only from localhost)
    sudo ufw allow from 127.0.0.1 to any port 2376
    
    # Internal Docker network
    sudo ufw allow from **********/16
    
    # Monitoring (restricted)
    sudo ufw allow from ${DB_VM_IP} to any port 9090
    sudo ufw allow from ${DB_VM_IP} to any port 3002
    
    print_status "Firewall configured"
}

# Security hardening
security_hardening() {
    print_info "Applying security hardening..."
    
    # Install fail2ban
    sudo apt install -y fail2ban
    
    # Configure fail2ban for SSH
    sudo tee /etc/fail2ban/jail.local > /dev/null <<EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3
bantime = 3600

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 3
bantime = 3600
EOF
    
    # Start fail2ban
    sudo systemctl enable fail2ban
    sudo systemctl start fail2ban
    
    # Configure automatic security updates
    sudo apt install -y unattended-upgrades
    sudo dpkg-reconfigure -plow unattended-upgrades
    
    # Disable root login and password authentication (if using SSH keys)
    print_warning "Consider disabling root login and password authentication in /etc/ssh/sshd_config"
    
    # Set up log rotation
    sudo tee /etc/logrotate.d/promotun > /dev/null <<EOF
/opt/promotun/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 0644 ${APP_USER} ${APP_USER}
    postrotate
        docker-compose -f /opt/promotun/docker-compose.production.yml restart nginx
    endscript
}
EOF
    
    print_status "Security hardening applied"
}

# Create application user and directories
setup_application_user() {
    print_info "Setting up application user and directories..."

    # Create application user
    sudo useradd -r -s /bin/bash -d ${APP_DIR} -m ${APP_USER} 2>/dev/null || true
    sudo usermod -aG docker ${APP_USER}

    # Create application directories
    sudo mkdir -p ${APP_DIR}/{logs,backups,ssl,monitoring}
    sudo mkdir -p ${APP_DIR}/nginx/{sites-enabled,conf.d,ssl}
    sudo chown -R ${APP_USER}:${APP_USER} ${APP_DIR}
    sudo chmod 755 ${APP_DIR}

    # Create nginx directories on host system (for dataxion compatibility)
    sudo mkdir -p /etc/nginx/{sites-available,sites-enabled,conf.d}
    sudo mkdir -p /var/www/certbot
    sudo chown -R www-data:www-data /var/www/certbot

    print_status "Application user and directories created"
}

# Install SSL certificate tools
install_ssl_tools() {
    print_info "Installing SSL certificate tools..."
    
    # Install Certbot
    sudo apt install -y certbot python3-certbot-nginx
    
    # Create SSL directory
    sudo mkdir -p /etc/nginx/ssl
    sudo chown -R ${APP_USER}:${APP_USER} /etc/nginx/ssl
    
    print_status "SSL tools installed"
}

# Configure system limits
configure_system_limits() {
    print_info "Configuring system limits..."
    
    # Increase file descriptor limits
    sudo tee -a /etc/security/limits.conf > /dev/null <<EOF
${APP_USER} soft nofile 65536
${APP_USER} hard nofile 65536
${APP_USER} soft nproc 4096
${APP_USER} hard nproc 4096
EOF
    
    # Configure sysctl for better performance
    sudo tee /etc/sysctl.d/99-promotun.conf > /dev/null <<EOF
# Network optimizations
net.core.somaxconn = 1024
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 1024
net.ipv4.tcp_keepalive_time = 600
net.ipv4.tcp_keepalive_intvl = 60
net.ipv4.tcp_keepalive_probes = 3

# Memory optimizations
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5

# File system optimizations
fs.file-max = 65536
EOF
    
    sudo sysctl -p /etc/sysctl.d/99-promotun.conf
    
    print_status "System limits configured"
}

# Install monitoring tools
install_monitoring() {
    print_info "Installing monitoring tools..."
    
    # Install htop, iotop, and other monitoring tools
    sudo apt install -y htop iotop nethogs ncdu tree
    
    # Install node_exporter for Prometheus monitoring
    NODE_EXPORTER_VERSION="1.6.1"
    wget https://github.com/prometheus/node_exporter/releases/download/v${NODE_EXPORTER_VERSION}/node_exporter-${NODE_EXPORTER_VERSION}.linux-amd64.tar.gz
    tar xvfz node_exporter-${NODE_EXPORTER_VERSION}.linux-amd64.tar.gz
    sudo mv node_exporter-${NODE_EXPORTER_VERSION}.linux-amd64/node_exporter /usr/local/bin/
    rm -rf node_exporter-${NODE_EXPORTER_VERSION}*
    
    # Create node_exporter service
    sudo tee /etc/systemd/system/node_exporter.service > /dev/null <<EOF
[Unit]
Description=Node Exporter
Wants=network-online.target
After=network-online.target

[Service]
User=nobody
Group=nobody
Type=simple
ExecStart=/usr/local/bin/node_exporter --web.listen-address=127.0.0.1:9100

[Install]
WantedBy=multi-user.target
EOF
    
    sudo systemctl daemon-reload
    sudo systemctl enable node_exporter
    sudo systemctl start node_exporter
    
    print_status "Monitoring tools installed"
}

# Setup backup script
setup_backup() {
    print_info "Setting up backup system..."
    
    sudo tee ${APP_DIR}/backup.sh > /dev/null <<'EOF'
#!/bin/bash

# PromoTun Backup Script
BACKUP_DIR="/opt/promotun/backups"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

# Create backup directory
mkdir -p ${BACKUP_DIR}

# Backup Docker volumes
docker run --rm -v promotun_backend_uploads:/data -v ${BACKUP_DIR}:/backup alpine tar czf /backup/uploads_${DATE}.tar.gz -C /data .
docker run --rm -v promotun_backend_logs:/data -v ${BACKUP_DIR}:/backup alpine tar czf /backup/logs_${DATE}.tar.gz -C /data .
docker run --rm -v promotun_grafana_data:/data -v ${BACKUP_DIR}:/backup alpine tar czf /backup/grafana_${DATE}.tar.gz -C /data .

# Backup configuration files
tar czf ${BACKUP_DIR}/config_${DATE}.tar.gz -C /opt/promotun docker-compose.production.yml .env.production nginx/

# Clean old backups
find ${BACKUP_DIR} -name "*.tar.gz" -mtime +${RETENTION_DAYS} -delete

echo "Backup completed: ${DATE}"
EOF
    
    sudo chmod +x ${APP_DIR}/backup.sh
    sudo chown ${APP_USER}:${APP_USER} ${APP_DIR}/backup.sh
    
    # Setup cron job for daily backups
    sudo -u ${APP_USER} crontab -l 2>/dev/null | { cat; echo "0 2 * * * ${APP_DIR}/backup.sh >> ${APP_DIR}/logs/backup.log 2>&1"; } | sudo -u ${APP_USER} crontab -
    
    print_status "Backup system configured"
}

# Main execution
main() {
    print_info "Starting PromoTun Application VM setup..."
    print_info "VM IP: ${APP_VM_IP}"
    print_info "Database VM IP: ${DB_VM_IP}"
    print_info "Domain: ${DOMAIN}"
    echo
    
    check_root
    update_system
    install_docker
    install_docker_compose
    configure_firewall
    security_hardening
    setup_application_user
    install_ssl_tools
    configure_system_limits
    install_monitoring
    setup_backup
    
    print_status "Application VM setup completed successfully!"
    echo
    print_info "Next steps:"
    echo "1. Reboot the system to apply all changes"
    echo "2. Copy your application code to ${APP_DIR}"
    echo "3. Configure SSL certificates"
    echo "4. Update .env.production with your actual values"
    echo "5. Deploy the application using docker-compose"
    echo
    print_warning "Please reboot the system now: sudo reboot"
}

# Run main function
main "$@"
