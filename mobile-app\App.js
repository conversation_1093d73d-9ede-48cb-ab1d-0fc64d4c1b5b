import React, { useEffect, useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { Provider as PaperProvider } from 'react-native-paper';
import { QueryClient, QueryClientProvider } from 'react-query';
import FlashMessage from 'react-native-flash-message';
import * as SplashScreen from 'expo-splash-screen';
import * as Font from 'expo-font';
import * as Notifications from 'expo-notifications';
import { I18nextProvider } from 'react-i18next';

import AppNavigator from './src/navigation/AppNavigator';
import { AuthProvider } from './src/store/authStore';
import { LocationProvider } from './src/store/locationStore';
import { theme } from './src/constants/theme';
import i18n from './src/locales/i18n';
import { initializeNotifications } from './src/services/notificationService';
import { loadFonts } from './src/utils/fontLoader';

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

// Configure notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    },
  },
});

export default function App() {
  const [appIsReady, setAppIsReady] = useState(false);

  useEffect(() => {
    async function prepare() {
      try {
        // Load fonts
        await loadFonts();
        
        // Initialize notifications
        await initializeNotifications();
        
        // Pre-load any other resources here
        
        // Artificially delay for demo purposes
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (e) {
        console.warn('Error during app initialization:', e);
      } finally {
        setAppIsReady(true);
      }
    }

    prepare();
  }, []);

  const onLayoutRootView = React.useCallback(async () => {
    if (appIsReady) {
      // Hide the splash screen once the app is ready
      await SplashScreen.hideAsync();
    }
  }, [appIsReady]);

  if (!appIsReady) {
    return null;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <I18nextProvider i18n={i18n}>
        <PaperProvider theme={theme}>
          <AuthProvider>
            <LocationProvider>
              <NavigationContainer onReady={onLayoutRootView}>
                <StatusBar style="auto" />
                <AppNavigator />
                <FlashMessage position="top" />
              </NavigationContainer>
            </LocationProvider>
          </AuthProvider>
        </PaperProvider>
      </I18nextProvider>
    </QueryClientProvider>
  );
}
