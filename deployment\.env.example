# PromoTun Docker Deployment Environment Configuration Template
# Copy this file to .env and fill in your actual values

# ===================================
# Database Configuration
# ===================================
POSTGRES_DB=promotun
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your-secure-database-password

# ===================================
# Redis Configuration
# ===================================
REDIS_PASSWORD=your-secure-redis-password

# ===================================
# JWT Configuration
# ===================================
JWT_SECRET=your-super-secret-jwt-key-for-production-change-this

# ===================================
# API Configuration
# ===================================
NODE_ENV=production
API_URL=http://localhost/api
SOCKET_URL=http://localhost
FRONTEND_URL=http://localhost

# ===================================
# SSL Configuration (for production)
# ===================================
SSL_EMAIL=<EMAIL>
DOMAIN=yourdomain.com

# ===================================
# Monitoring Configuration
# ===================================
GRAFANA_PASSWORD=your-secure-grafana-password

# ===================================
# Firebase Configuration (for push notifications)
# ===================================
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY=-----BEGIN PRIVATE KEY-----\nYOUR_FIREBASE_PRIVATE_KEY\n-----END PRIVATE KEY-----\n

# ===================================
# Email Configuration
# ===================================
SENDGRID_API_KEY=your-sendgrid-api-key
FROM_EMAIL=<EMAIL>
FROM_NAME=PromoTun

# ===================================
# External APIs
# ===================================
GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# ===================================
# Security Configuration
# ===================================
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-for-production

# ===================================
# Logging Configuration
# ===================================
LOG_LEVEL=info

# ===================================
# Rate Limiting
# ===================================
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
