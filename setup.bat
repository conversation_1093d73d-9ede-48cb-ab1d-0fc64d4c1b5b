@echo off
setlocal enabledelayedexpansion

REM PromoTun Project Setup Script for Windows
REM This script sets up the development environment for PromoTun

echo.
echo 🚀 PromoTun Project Setup
echo =========================
echo.

REM Check if Node.js is installed
echo 🔍 Checking prerequisites...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 16+ from https://nodejs.org/
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo ✅ Node.js is installed: !NODE_VERSION!
)

REM Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not installed
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
    echo ✅ npm is installed: !NPM_VERSION!
)

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Docker is not installed. Install Docker Desktop for full deployment capabilities
) else (
    for /f "tokens=*" %%i in ('docker --version') do set DOCKER_VERSION=%%i
    echo ✅ Docker is installed: !DOCKER_VERSION!
)

echo.
echo 📁 Setting up environment files...

REM Setup backend environment
if not exist "backend\.env" (
    copy "backend\.env.example" "backend\.env" >nul
    echo ✅ Created backend\.env from template
) else (
    echo ⚠️  backend\.env already exists
)

REM Setup merchant portal environment
if not exist "merchant-portal\.env.local" (
    copy "merchant-portal\.env.example" "merchant-portal\.env.local" >nul
    echo ✅ Created merchant-portal\.env.local from template
) else (
    echo ⚠️  merchant-portal\.env.local already exists
)

REM Setup deployment environment
if not exist "deployment\.env" (
    copy "deployment\.env.example" "deployment\.env" >nul
    echo ✅ Created deployment\.env from template
) else (
    echo ⚠️  deployment\.env already exists
)

echo.
echo 📁 Creating necessary directories...
if not exist "backend\logs" mkdir "backend\logs"
if not exist "backend\uploads" mkdir "backend\uploads"
if not exist "data" mkdir "data"
echo ✅ Directories created

echo.
echo 📦 Installing dependencies...

echo Installing backend dependencies...
cd backend
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install backend dependencies
    cd ..
    pause
    exit /b 1
)
cd ..
echo ✅ Backend dependencies installed

echo Installing merchant portal dependencies...
cd merchant-portal
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install merchant portal dependencies
    cd ..
    pause
    exit /b 1
)
cd ..
echo ✅ Merchant portal dependencies installed

REM Install admin dashboard dependencies if package.json exists
if exist "admin-dashboard\package.json" (
    echo Installing admin dashboard dependencies...
    cd admin-dashboard
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ Failed to install admin dashboard dependencies
        cd ..
        pause
        exit /b 1
    )
    cd ..
    echo ✅ Admin dashboard dependencies installed
)

echo.
echo ✅ Setup completed successfully!
echo.
echo ℹ️  Next steps:
echo 1. Edit environment files with your configuration:
echo    - backend\.env
echo    - merchant-portal\.env.local
echo    - deployment\.env
echo.
echo 2. Start the development servers:
echo    Backend:         cd backend ^&^& npm start
echo    Merchant Portal: cd merchant-portal ^&^& npm run dev
echo.
echo 3. Or use Docker for full deployment:
echo    cd deployment ^&^& docker-compose up -d
echo.
echo 🎉 Happy coding!
echo.
pause
