# GitLab CI/CD Environment Variables Setup

This document describes how to configure environment variables in GitLab CI/CD for secure PromoTun deployment.

## Required GitLab CI/CD Variables

### SSH Access Variables
Set these in GitLab Project Settings > CI/CD > Variables:

```
STAGING_SSH_PRIVATE_KEY (Type: File, Protected: Yes, Masked: No)
PRODUCTION_SSH_PRIVATE_KEY (Type: File, Protected: Yes, Masked: No)
```

### Production Database Variables
```
PRODUCTION_DB_HOST (Type: Variable, Protected: Yes, Masked: No)
PRODUCTION_DB_PORT (Type: Variable, Protected: Yes, Masked: No)
PRODUCTION_DB_NAME (Type: Variable, Protected: Yes, Masked: No)
PRODUCTION_DB_USER (Type: Variable, Protected: Yes, Masked: No)
PRODUCTION_DB_PASSWORD (Type: Variable, Protected: Yes, Masked: Yes)
```

### Production Security Variables
```
PRODUCTION_REDIS_PASSWORD (Type: Variable, Protected: Yes, Masked: Yes)
PRODUCTION_JWT_SECRET (Type: Variable, Protected: Yes, Masked: Yes)
PRODUCTION_SESSION_SECRET (Type: Variable, Protected: Yes, Masked: Yes)
PRODUCTION_NEXTAUTH_SECRET (Type: Variable, Protected: Yes, Masked: Yes)
```

### Production Firebase Variables
```
PRODUCTION_FIREBASE_PROJECT_ID (Type: Variable, Protected: Yes, Masked: No)
PRODUCTION_FIREBASE_CLIENT_EMAIL (Type: Variable, Protected: Yes, Masked: No)
PRODUCTION_FIREBASE_PRIVATE_KEY (Type: Variable, Protected: Yes, Masked: Yes)
```

### Production External API Variables
```
PRODUCTION_SENDGRID_API_KEY (Type: Variable, Protected: Yes, Masked: Yes)
PRODUCTION_GOOGLE_MAPS_API_KEY (Type: Variable, Protected: Yes, Masked: Yes)
```

### Production Email Variables
```
PRODUCTION_FROM_EMAIL (Type: Variable, Protected: Yes, Masked: No)
PRODUCTION_FROM_NAME (Type: Variable, Protected: Yes, Masked: No)
```

### Production Monitoring Variables
```
PRODUCTION_GRAFANA_PASSWORD (Type: Variable, Protected: Yes, Masked: Yes)
```

### Staging Variables (Optional)
For staging environment, create similar variables with `STAGING_` prefix:
```
STAGING_DB_PASSWORD
STAGING_REDIS_PASSWORD
STAGING_JWT_SECRET
# ... etc
```

## How to Set Variables in GitLab

1. Go to your GitLab project
2. Navigate to **Settings** > **CI/CD**
3. Expand the **Variables** section
4. Click **Add variable**
5. Configure each variable:
   - **Key**: Variable name (e.g., `PRODUCTION_DB_PASSWORD`)
   - **Value**: The actual value
   - **Type**: Variable (for text) or File (for SSH keys)
   - **Environment scope**: All (default) or specific environments
   - **Protect variable**: Yes (for production variables)
   - **Mask variable**: Yes (for sensitive values like passwords/keys)

## Security Best Practices

### Variable Protection
- **Protected**: Only available to protected branches (main, production)
- **Masked**: Values are hidden in job logs
- **File type**: For SSH keys and certificates

### Naming Convention
- Use `PRODUCTION_` prefix for production variables
- Use `STAGING_` prefix for staging variables
- Use descriptive names (e.g., `PRODUCTION_DB_PASSWORD` not `DB_PASS`)

### Value Requirements
- **Passwords**: Minimum 32 characters, alphanumeric + symbols
- **Secrets**: Minimum 64 characters for JWT/session secrets
- **API Keys**: Use actual keys from service providers
- **SSH Keys**: Use dedicated deployment keys, not personal keys

## Generating Secure Values

Use these commands to generate secure values:

```bash
# Generate 64-character secret for JWT/Session secrets
openssl rand -base64 64

# Generate 32-character password for database/Redis
openssl rand -base64 32

# Generate 16-character password for Grafana
openssl rand -base64 16
```

## Environment File Creation

The deployment script automatically creates `.env.production` from GitLab CI/CD variables. You can also manually create it using:

```bash
cd /opt/promotun/deployment
./scripts/setup-environment.sh create
./scripts/setup-environment.sh validate
```

## Troubleshooting

### Common Issues

1. **Variable not found**: Check variable name spelling and scope
2. **Masked variable in logs**: This is expected for sensitive values
3. **SSH key format**: Ensure private key includes header/footer lines
4. **Firebase private key**: Escape newlines properly in JSON format

### Validation Commands

```bash
# Test environment setup
./deployment/scripts/setup-environment.sh validate

# Test Docker Compose configuration
cd deployment && docker-compose -f docker-compose.production.yml config

# Test SSH connection
ssh -i ~/.ssh/production_key promotun@your-server-ip
```

## Example Variable Values

### SSH Private Key Format
```
-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAFwAAAAdzc2gtcn
...
-----END OPENSSH PRIVATE KEY-----
```

### Firebase Private Key Format
```
-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...\n-----END PRIVATE KEY-----
```

### Database Connection String Example
```
PRODUCTION_DB_HOST=**************
PRODUCTION_DB_PORT=5432
PRODUCTION_DB_NAME=promotun_production
PRODUCTION_DB_USER=promotun_user
PRODUCTION_DB_PASSWORD=your-secure-32-char-password
```

## Security Checklist

- [ ] All production variables are marked as "Protected"
- [ ] All sensitive values are marked as "Masked"
- [ ] SSH keys are stored as "File" type variables
- [ ] Passwords meet minimum length requirements
- [ ] API keys are from actual service accounts
- [ ] Firebase service account has minimal required permissions
- [ ] Database user has only necessary privileges
- [ ] Variables are scoped to appropriate environments
