#!/bin/bash

# PromoTun Production Deployment Script
# This script deploys the PromoTun application to production

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMMIT_SHA=${1:-latest}
APP_DIR="/opt/promotun"
BACKUP_DIR="/opt/promotun/backups"
COMPOSE_FILE="docker-compose.production.yml"
ENV_FILE=".env.production"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running as promotun user
check_user() {
    if [[ $(whoami) != "promotun" ]]; then
        print_error "This script must be run as the promotun user"
        print_info "Switch to promotun user: sudo -u promotun -i"
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check if in correct directory
    if [[ ! -f "${COMPOSE_FILE}" ]]; then
        print_error "docker-compose.production.yml not found. Please run from ${APP_DIR}"
        exit 1
    fi
    
    # Check or create environment file
    if [[ ! -f "${ENV_FILE}" ]]; then
        print_warning ".env.production not found. Creating from CI/CD variables..."
        create_env_from_ci_variables
    fi
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed or not in PATH"
        exit 1
    fi
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running"
        exit 1
    fi
    
    print_status "Prerequisites check passed"
}

# Create environment file from CI/CD variables
create_env_from_ci_variables() {
    print_info "Creating .env.production from CI/CD variables..."

    cat > ${ENV_FILE} << EOF
# PromoTun Production Environment Variables
# Generated from GitLab CI/CD variables

# Domain Configuration
DOMAIN_NAME=${DOMAIN_NAME:-promodetect.com}

# Database Configuration
DB_HOST=${PRODUCTION_DB_HOST:-${DB_VM_HOST}}
DB_PORT=${PRODUCTION_DB_PORT:-5432}
DB_NAME=${PRODUCTION_DB_NAME:-promotun_production}
DB_USER=${PRODUCTION_DB_USER:-promotun_user}
DB_PASSWORD=${PRODUCTION_DB_PASSWORD}
DB_SSL=${PRODUCTION_DB_SSL:-true}
DB_CONNECTION_TIMEOUT=${PRODUCTION_DB_CONNECTION_TIMEOUT:-30000}
DB_POOL_MIN=${PRODUCTION_DB_POOL_MIN:-2}
DB_POOL_MAX=${PRODUCTION_DB_POOL_MAX:-10}

# Redis Configuration
REDIS_PASSWORD=${PRODUCTION_REDIS_PASSWORD}

# Security Secrets
JWT_SECRET=${PRODUCTION_JWT_SECRET}
SESSION_SECRET=${PRODUCTION_SESSION_SECRET}
NEXTAUTH_SECRET=${PRODUCTION_NEXTAUTH_SECRET}
BCRYPT_ROUNDS=${PRODUCTION_BCRYPT_ROUNDS:-12}

# Firebase Configuration
FIREBASE_PROJECT_ID=${PRODUCTION_FIREBASE_PROJECT_ID}
FIREBASE_CLIENT_EMAIL=${PRODUCTION_FIREBASE_CLIENT_EMAIL}
FIREBASE_PRIVATE_KEY=${PRODUCTION_FIREBASE_PRIVATE_KEY}

# External API Keys
SENDGRID_API_KEY=${PRODUCTION_SENDGRID_API_KEY}
GOOGLE_MAPS_API_KEY=${PRODUCTION_GOOGLE_MAPS_API_KEY}

# Email Configuration
FROM_EMAIL=${PRODUCTION_FROM_EMAIL:-<EMAIL>}
FROM_NAME=${PRODUCTION_FROM_NAME:-PromoTun Platform}

# Application Configuration
LOG_LEVEL=${PRODUCTION_LOG_LEVEL:-info}
RATE_LIMIT_WINDOW_MS=${PRODUCTION_RATE_LIMIT_WINDOW_MS:-900000}
RATE_LIMIT_MAX_REQUESTS=${PRODUCTION_RATE_LIMIT_MAX_REQUESTS:-100}

# Monitoring Configuration
GRAFANA_PASSWORD=${PRODUCTION_GRAFANA_PASSWORD}

# Docker Image Tags
BACKEND_IMAGE=${BACKEND_IMAGE:-${CI_REGISTRY_IMAGE}/backend:latest}
MERCHANT_PORTAL_IMAGE=${MERCHANT_PORTAL_IMAGE:-${CI_REGISTRY_IMAGE}/merchant-portal:latest}
ADMIN_DASHBOARD_IMAGE=${ADMIN_DASHBOARD_IMAGE:-${CI_REGISTRY_IMAGE}/admin-dashboard:latest}
MOBILE_APP_IMAGE=${MOBILE_APP_IMAGE:-${CI_REGISTRY_IMAGE}/mobile-app:latest}
EOF

    print_status "Environment file created from CI/CD variables"
}

# Test database connection
test_database_connection() {
    print_info "Testing database connection..."
    
    # Source environment variables
    source ${ENV_FILE}
    
    # Test PostgreSQL connection
    if command -v psql &> /dev/null; then
        if PGPASSWORD=${DB_PASSWORD} psql -h ${DB_HOST} -U ${DB_USER} -d ${DB_NAME} -c "SELECT 1;" &> /dev/null; then
            print_status "Database connection successful"
        else
            print_error "Cannot connect to database at ${DB_HOST}:${DB_PORT}"
            print_info "Please verify database VM is running and accessible"
            exit 1
        fi
    else
        print_warning "psql not installed, skipping database connection test"
    fi
}

# Create backup before deployment
create_backup() {
    print_info "Creating backup before deployment..."
    
    DATE=$(date +%Y%m%d_%H%M%S)
    BACKUP_NAME="pre-deployment_${DATE}"
    
    # Create backup directory
    mkdir -p ${BACKUP_DIR}
    
    # Backup current containers (if running)
    if docker-compose -f ${COMPOSE_FILE} ps | grep -q "Up"; then
        print_info "Backing up current deployment..."
        
        # Backup volumes
        docker run --rm -v promotun_backend_uploads:/data -v ${BACKUP_DIR}:/backup alpine tar czf /backup/${BACKUP_NAME}_uploads.tar.gz -C /data .
        docker run --rm -v promotun_backend_logs:/data -v ${BACKUP_DIR}:/backup alpine tar czf /backup/${BACKUP_NAME}_logs.tar.gz -C /data .
        docker run --rm -v promotun_grafana_data:/data -v ${BACKUP_DIR}:/backup alpine tar czf /backup/${BACKUP_NAME}_grafana.tar.gz -C /data .
        
        print_status "Backup created: ${BACKUP_NAME}"
    else
        print_info "No running containers to backup"
    fi
}

# Pull latest images
pull_images() {
    print_info "Pulling latest Docker images..."
    
    # Pull base images
    docker pull redis:7-alpine
    docker pull nginx:alpine
    docker pull prom/prometheus:latest
    docker pull grafana/grafana:latest
    
    print_status "Base images updated"
}

# Build application images
build_images() {
    print_info "Building application images..."
    
    # Build images
    docker-compose -f ${COMPOSE_FILE} build --no-cache
    
    if [[ $? -eq 0 ]]; then
        print_status "Application images built successfully"
    else
        print_error "Failed to build application images"
        exit 1
    fi
}

# Update Docker images for CI/CD deployment
update_images() {
    print_info "Updating Docker images for commit: ${COMMIT_SHA}"

    # Login to GitLab Container Registry if credentials are available
    if [[ -n "$CI_REGISTRY_PASSWORD" ]]; then
        echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY

        # Pull images with specific commit SHA
        docker pull $CI_REGISTRY_IMAGE/backend:${COMMIT_SHA} || docker pull $CI_REGISTRY_IMAGE/backend:latest
        docker pull $CI_REGISTRY_IMAGE/merchant-portal:${COMMIT_SHA} || docker pull $CI_REGISTRY_IMAGE/merchant-portal:latest
        docker pull $CI_REGISTRY_IMAGE/admin-dashboard:${COMMIT_SHA} || docker pull $CI_REGISTRY_IMAGE/admin-dashboard:latest

        # Update docker-compose to use registry images
        export BACKEND_IMAGE="$CI_REGISTRY_IMAGE/backend:${COMMIT_SHA}"
        export MERCHANT_PORTAL_IMAGE="$CI_REGISTRY_IMAGE/merchant-portal:${COMMIT_SHA}"
        export ADMIN_DASHBOARD_IMAGE="$CI_REGISTRY_IMAGE/admin-dashboard:${COMMIT_SHA}"

        print_status "Docker images updated from registry"
    else
        print_info "No registry credentials found, building images locally"
    fi
}

# Deploy application
deploy_application() {
    print_info "Deploying application..."

    # Ensure nginx configuration directories exist
    mkdir -p nginx/{sites-enabled,conf.d,ssl}

    # Copy nginx configurations if they don't exist
    if [[ ! -f "nginx/sites-enabled/promodetect.com" ]]; then
        print_warning "PromoTun site configuration not found, copying from template"
        cp nginx/sites-enabled/promodetect.com.template nginx/sites-enabled/promodetect.com 2>/dev/null || true
    fi

    # Zero-downtime deployment strategy
    print_info "Performing zero-downtime deployment..."

    # Scale up new containers alongside old ones
    if [[ -n "$CI_REGISTRY_PASSWORD" ]]; then
        # Use registry images with environment variables
        BACKEND_IMAGE="$CI_REGISTRY_IMAGE/backend:${COMMIT_SHA}" \
        MERCHANT_PORTAL_IMAGE="$CI_REGISTRY_IMAGE/merchant-portal:${COMMIT_SHA}" \
        ADMIN_DASHBOARD_IMAGE="$CI_REGISTRY_IMAGE/admin-dashboard:${COMMIT_SHA}" \
        docker-compose -f ${COMPOSE_FILE} up -d --no-deps backend merchant-portal admin-dashboard
    else
        # Build and deploy locally
        docker-compose -f ${COMPOSE_FILE} up -d --build
    fi

    if [[ $? -eq 0 ]]; then
        print_status "Application deployed successfully"
    else
        print_error "Failed to deploy application"
        exit 1
    fi
}

# Wait for services to be ready
wait_for_services() {
    print_info "Waiting for services to be ready..."
    
    # Wait for backend health check
    for i in {1..30}; do
        if curl -f -s http://localhost:5000/health > /dev/null; then
            print_status "Backend service is ready"
            break
        fi
        
        if [[ $i -eq 30 ]]; then
            print_error "Backend service failed to start"
            exit 1
        fi
        
        sleep 10
    done
    
    # Wait for frontend services
    sleep 30
    
    if curl -f -s http://localhost:3000/health > /dev/null; then
        print_status "Merchant portal is ready"
    else
        print_warning "Merchant portal health check failed"
    fi
    
    if curl -f -s http://localhost:3001/health > /dev/null; then
        print_status "Admin dashboard is ready"
    else
        print_warning "Admin dashboard health check failed"
    fi
}

# Verify deployment
verify_deployment() {
    print_info "Verifying deployment..."
    
    # Check container status
    echo
    print_info "Container Status:"
    docker-compose -f ${COMPOSE_FILE} ps
    
    # Check service health
    echo
    print_info "Service Health Checks:"
    
    # Backend API
    if curl -f -s http://localhost:5000/health | grep -q "OK"; then
        print_status "Backend API: Healthy"
    else
        print_error "Backend API: Unhealthy"
    fi
    
    # Merchant Portal
    if curl -f -s http://localhost:3000 > /dev/null; then
        print_status "Merchant Portal: Accessible"
    else
        print_error "Merchant Portal: Not accessible"
    fi
    
    # Admin Dashboard
    if curl -f -s http://localhost:3001 > /dev/null; then
        print_status "Admin Dashboard: Accessible"
    else
        print_error "Admin Dashboard: Not accessible"
    fi
    
    # Nginx
    if curl -f -s http://localhost > /dev/null; then
        print_status "Nginx Proxy: Working"
    else
        print_error "Nginx Proxy: Not working"
    fi
    
    # Redis
    if docker exec promotun-redis redis-cli ping | grep -q "PONG"; then
        print_status "Redis: Connected"
    else
        print_error "Redis: Connection failed"
    fi
}

# Show deployment summary
show_summary() {
    print_info "Deployment Summary"
    echo "=================="
    echo
    
    # Source environment variables
    source ${ENV_FILE}
    
    echo "🌐 Application URLs:"
    echo "   Main Site: https://${DOMAIN_NAME}"
    echo "   Admin Panel: https://${DOMAIN_NAME}/admin"
    echo "   API: https://${DOMAIN_NAME}/api"
    echo "   Monitoring: https://${DOMAIN_NAME}/grafana"
    echo
    
    echo "📊 Service Status:"
    docker-compose -f ${COMPOSE_FILE} ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"
    echo
    
    echo "💾 Resource Usage:"
    docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"
    echo
    
    echo "📋 Management Commands:"
    echo "   View logs: docker-compose -f ${COMPOSE_FILE} logs -f [service]"
    echo "   Restart service: docker-compose -f ${COMPOSE_FILE} restart [service]"
    echo "   Scale service: docker-compose -f ${COMPOSE_FILE} up -d --scale [service]=N"
    echo "   Stop all: docker-compose -f ${COMPOSE_FILE} down"
    echo
    
    echo "🔧 Maintenance:"
    echo "   Backup: ${APP_DIR}/backup.sh"
    echo "   SSL Renewal: /usr/local/bin/renew-ssl.sh"
    echo "   Logs: ${APP_DIR}/logs/"
    echo
}

# Main execution
main() {
    print_info "Starting PromoTun production deployment..."
    echo
    
    check_user
    check_prerequisites
    test_database_connection
    create_backup
    update_images
    pull_images
    build_images
    deploy_application
    wait_for_services
    verify_deployment
    show_summary
    
    print_status "Production deployment completed successfully! 🎉"
    echo
    print_info "Your PromoTun application is now running in production mode."
    print_warning "Monitor the logs and services for any issues."
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "status")
        cd ${APP_DIR}
        docker-compose -f ${COMPOSE_FILE} ps
        ;;
    "logs")
        cd ${APP_DIR}
        docker-compose -f ${COMPOSE_FILE} logs -f ${2:-}
        ;;
    "restart")
        cd ${APP_DIR}
        docker-compose -f ${COMPOSE_FILE} restart ${2:-}
        ;;
    "stop")
        cd ${APP_DIR}
        docker-compose -f ${COMPOSE_FILE} down
        ;;
    "backup")
        cd ${APP_DIR}
        ./backup.sh
        ;;
    *)
        echo "Usage: $0 {deploy|status|logs|restart|stop|backup} [service]"
        exit 1
        ;;
esac
