const express = require('express');
const { query } = require('../database/connection');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Get system analytics (admin only)
router.get('/system', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    // Get user statistics
    const userStats = await query(`
      SELECT 
        COUNT(*) as total_users,
        COUNT(*) FILTER (WHERE user_type = 'consumer') as consumers,
        COUNT(*) FILTER (WHERE user_type = 'merchant') as merchants,
        COUNT(*) FILTER (WHERE is_verified = true) as verified_users,
        COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '30 days') as new_users_30d
      FROM users
      WHERE is_active = true
    `);

    // Get promotion statistics
    const promotionStats = await query(`
      SELECT 
        COUNT(*) as total_promotions,
        COUNT(*) FILTER (WHERE is_active = true AND start_date <= CURRENT_TIMESTAMP AND end_date >= CURRENT_TIMESTAMP) as active_promotions,
        COUNT(*) FILTER (WHERE status = 'pending') as pending_promotions,
        COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '30 days') as new_promotions_30d
      FROM promotions
    `);

    // Get engagement statistics
    const engagementStats = await query(`
      SELECT 
        COUNT(DISTINCT uf.user_id) as users_with_favorites,
        COUNT(*) as total_favorites,
        COUNT(DISTINCT fb.user_id) as users_with_feedback,
        COUNT(fb.*) as total_feedback
      FROM user_favorites uf
      FULL OUTER JOIN user_feedback fb ON uf.user_id = fb.user_id
    `);

    const analytics = {
      users: userStats.rows[0],
      promotions: promotionStats.rows[0],
      engagement: engagementStats.rows[0]
    };

    res.json({
      success: true,
      data: { analytics }
    });
  } catch (error) {
    logger.error('Get system analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Track user event
router.post('/track', authenticateToken, async (req, res) => {
  try {
    const { eventType, eventData, promotionId, sessionId } = req.body;

    await query(`
      INSERT INTO user_analytics (
        user_id, event_type, event_data, promotion_id, session_id,
        ip_address, user_agent
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
    `, [
      req.user.id,
      eventType,
      eventData ? JSON.stringify(eventData) : null,
      promotionId,
      sessionId,
      req.ip,
      req.get('User-Agent')
    ]);

    res.json({
      success: true,
      message: 'Event tracked successfully'
    });
  } catch (error) {
    logger.error('Track event error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
