# Test Environment Configuration
NODE_ENV=test
PORT=5001
FRONTEND_URL=http://localhost:3000

# Test Database Configuration (GitLab CI/CD Services)
DB_HOST=postgres
DB_PORT=5432
DB_NAME=promotun_test
DB_USER=test_user
DB_PASSWORD=test_password
DB_SSL=false
DB_CONNECTION_TIMEOUT=30000
DB_POOL_MIN=1
DB_POOL_MAX=5

# Test Redis Configuration (GitLab CI/CD Services)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_URL=redis://redis:6379

# Security Secrets (Test Values)
JWT_SECRET=test-jwt-secret-key-for-testing-only-minimum-32-characters
SESSION_SECRET=test-session-secret-key-for-testing-only-minimum-32-characters
BCRYPT_ROUNDS=4

# Firebase Configuration (Test/Mock Values)
FIREBASE_PROJECT_ID=test-project
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----B<PERSON>IN PRIVATE KEY-----\ntest-private-key\n-----END PRIVATE KEY-----"

# External API Keys (Test/Mock Values)
SENDGRID_API_KEY=SG.test_sendgrid_api_key
GOOGLE_MAPS_API_KEY=test_google_maps_api_key

# Email Configuration (Test Values)
FROM_EMAIL=<EMAIL>
FROM_NAME=PromoTun Test

# Application Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# Production Flags (Test Configuration)
ENABLE_CORS=true
ENABLE_SWAGGER=false
MOCK_EXTERNAL_APIS=true
MOCK_DATABASE=false

# Test logging
LOG_LEVEL=error
