# Test Environment Configuration
NODE_ENV=test
PORT=5001
FRONTEND_URL=http://localhost:3000

# Test Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=promotun_test
DB_USER=postgres
DB_PASSWORD=password

# Test Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=1

# Test JWT Configuration
JWT_SECRET=test-jwt-secret-key-for-testing-only-change-in-production
JWT_EXPIRES_IN=1h

# Mock external services in test
MOCK_EXTERNAL_APIS=true
SENDGRID_API_KEY=test-key
FROM_EMAIL=<EMAIL>
FROM_NAME=PromoTun Test

# Test logging
LOG_LEVEL=error
