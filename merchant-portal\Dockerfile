# PromoTun Merchant Portal - Multi-stage Production Dockerfile
# Next.js React application with Material-UI

# ===================================
# Base Stage - Common dependencies
# ===================================
FROM node:18-alpine AS base

# Install system dependencies and security updates
RUN apk update && apk upgrade && \
    apk add --no-cache \
    curl=8.5.0-r0 \
    dumb-init=1.2.5-r2 \
    libc6-compat=1.2.4-r2 \
    && rm -rf /var/cache/apk/*

# Create app user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S promotun -u 1001 -G nodejs

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# ===================================
# Development Stage
# ===================================
FROM base AS development

# Install all dependencies (including dev dependencies)
RUN npm ci --include=dev

# Copy source code
COPY . .

# Change ownership to app user
RUN chown -R promotun:nodejs /app

# Switch to non-root user
USER promotun

# Expose development port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000/health || curl -f http://localhost:3000 || exit 1

# Development command
CMD ["dumb-init", "npm", "run", "dev"]

# ===================================
# Dependencies Stage - Production dependencies only
# ===================================
FROM base AS dependencies

# Set NODE_ENV to production for optimal npm install
ENV NODE_ENV=production

# Install only production dependencies
RUN npm ci --only=production --no-audit --no-fund && \
    npm cache clean --force

# ===================================
# Build Stage - Build Next.js application
# ===================================
FROM base AS build

# Set NODE_ENV for build optimizations
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Install all dependencies for building
RUN npm ci --include=dev

# Copy source code
COPY . .

# Build the Next.js application
RUN npm run build && \
    npm prune --production

# ===================================
# Production Stage - Final optimized image
# ===================================
FROM node:18-alpine AS production

# Install runtime dependencies and security updates
RUN apk update && apk upgrade && \
    apk add --no-cache \
    curl=8.5.0-r0 \
    dumb-init=1.2.5-r2 \
    && rm -rf /var/cache/apk/*

# Create app user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S promotun -u 1001 -G nodejs

# Set working directory
WORKDIR /app

# Set production environment
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Copy production dependencies
COPY --from=dependencies --chown=promotun:nodejs /app/node_modules ./node_modules

# Copy built application
COPY --from=build --chown=promotun:nodejs /app/.next ./.next/
COPY --from=build --chown=promotun:nodejs /app/public ./public/
COPY --from=build --chown=promotun:nodejs /app/package*.json ./
COPY --from=build --chown=promotun:nodejs /app/next.config.js ./next.config.js

# Create a simple health check endpoint
RUN echo '{"status":"ok","service":"merchant-portal"}' > ./public/health.json

# Switch to non-root user
USER promotun

# Expose application port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000/health.json || curl -f http://localhost:3000 || exit 1

# Production command with proper signal handling
CMD ["dumb-init", "npm", "start"]

# ===================================
# Metadata
# ===================================
LABEL maintainer="PromoTun Development Team"
LABEL version="1.0.0"
LABEL description="PromoTun Merchant Portal - Next.js Production Ready"
LABEL org.opencontainers.image.source="https://gitlab.com/promotun/promotun-platform"
