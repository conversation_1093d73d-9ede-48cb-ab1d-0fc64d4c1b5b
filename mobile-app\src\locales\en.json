{"navigation": {"home": "Home", "search": "Search", "favorites": "Favorites", "profile": "Profile", "settings": "Settings", "notifications": "Notifications"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "firstName": "First Name", "lastName": "Last Name", "forgotPassword": "Forgot Password?", "dontHaveAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "signUp": "Sign Up", "signIn": "Sign In", "userType": "User Type", "consumer": "Consumer", "merchant": "Merchant", "preferredLanguage": "Preferred Language", "verifyEmail": "<PERSON><PERSON><PERSON>", "verificationCode": "Verification Code", "resendCode": "Resend Code", "emailVerified": "Email Verified Successfully", "loginSuccess": "Login Successful", "registrationSuccess": "Registration Successful", "logoutSuccess": "Logged Out Successfully"}, "home": {"title": "PromoTun", "subtitle": "Discover amazing deals near you", "featuredDeals": "Featured Deals", "categories": "Categories", "nearbyPromotions": "Nearby Promotions", "viewAll": "View All", "noPromotions": "No promotions found", "loading": "Loading...", "refresh": "Pull to refresh", "searchPlaceholder": "Search for deals, stores, or products..."}, "search": {"title": "Search", "placeholder": "What are you looking for?", "recentSearches": "Recent Searches", "popularSearches": "Popular Searches", "filters": "Filters", "sortBy": "Sort By", "category": "Category", "distance": "Distance", "priceRange": "Price Range", "rating": "Rating", "clearFilters": "Clear Filters", "applyFilters": "Apply Filters", "noResults": "No results found", "searchResults": "Search Results"}, "promotions": {"title": "Promotion Details", "description": "Description", "originalPrice": "Original Price", "discountedPrice": "Discounted Price", "discount": "Discount", "validUntil": "<PERSON>id <PERSON>", "termsConditions": "Terms & Conditions", "merchant": "Merchant", "location": "Location", "directions": "Get Directions", "share": "Share", "addToFavorites": "Add to Favorites", "removeFromFavorites": "Remove from Favorites", "reportPromotion": "Report Promotion", "expired": "Expired", "active": "Active", "comingSoon": "Coming Soon"}, "favorites": {"title": "Favorites", "noFavorites": "No favorites yet", "noFavoritesDescription": "Start adding promotions to your favorites to see them here", "removeFromFavorites": "Remove from Favorites", "viewPromotion": "View Promotion"}, "profile": {"title": "Profile", "editProfile": "Edit Profile", "personalInfo": "Personal Information", "preferences": "Preferences", "notifications": "Notifications", "language": "Language", "location": "Location", "privacy": "Privacy", "support": "Support", "about": "About", "version": "Version", "saveChanges": "Save Changes", "changesSaved": "Changes Saved Successfully"}, "settings": {"title": "Settings", "account": "Account", "notifications": "Notifications", "privacy": "Privacy", "language": "Language", "location": "Location", "support": "Support", "about": "About", "logout": "Logout", "deleteAccount": "Delete Account", "confirmLogout": "Are you sure you want to logout?", "confirmDelete": "Are you sure you want to delete your account? This action cannot be undone."}, "notifications": {"title": "Notifications", "markAllRead": "<PERSON> as <PERSON>", "noNotifications": "No notifications", "pushNotifications": "Push Notifications", "emailNotifications": "Email Notifications", "smsNotifications": "SMS Notifications", "promotionAlerts": "Promotion Alerts", "nearbyDeals": "Nearby Deals", "favoriteStores": "Favorite Stores", "priceDrops": "Price Drops"}, "categories": {"electronics": "Electronics", "fashion": "Fashion", "food": "Food & Dining", "health": "Health & Beauty", "home": "Home & Garden", "sports": "Sports & Fitness", "travel": "Travel & Tourism", "automotive": "Automotive", "entertainment": "Entertainment", "education": "Education"}, "common": {"ok": "OK", "cancel": "Cancel", "yes": "Yes", "no": "No", "save": "Save", "edit": "Edit", "delete": "Delete", "share": "Share", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "retry": "Retry", "refresh": "Refresh", "viewMore": "View More", "showLess": "Show Less", "selectAll": "Select All", "deselectAll": "Deselect All", "required": "Required", "optional": "Optional", "km": "km", "miles": "miles", "today": "Today", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "thisWeek": "This Week", "thisMonth": "This Month", "currency": "$"}, "errors": {"networkError": "Network error. Please check your connection.", "serverError": "Server error. Please try again later.", "invalidCredentials": "Invalid email or password.", "emailRequired": "Email is required.", "passwordRequired": "Password is required.", "passwordTooShort": "Password must be at least 8 characters.", "passwordsDoNotMatch": "Passwords do not match.", "invalidEmail": "Please enter a valid email address.", "userNotFound": "User not found.", "emailAlreadyExists": "Email already exists.", "locationPermissionDenied": "Location permission denied.", "cameraPermissionDenied": "Camera permission denied.", "storagePermissionDenied": "Storage permission denied.", "somethingWentWrong": "Something went wrong. Please try again."}, "permissions": {"location": {"title": "Location Permission", "message": "PromoTun needs access to your location to show nearby promotions.", "allow": "Allow", "deny": "<PERSON><PERSON>"}, "camera": {"title": "Camera Permission", "message": "PromoTun needs access to your camera to take photos.", "allow": "Allow", "deny": "<PERSON><PERSON>"}, "notifications": {"title": "Notification Permission", "message": "Promo<PERSON><PERSON> would like to send you notifications about new deals.", "allow": "Allow", "deny": "<PERSON><PERSON>"}}}