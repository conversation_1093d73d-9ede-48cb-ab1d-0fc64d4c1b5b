const express = require('express');
const { body, validationResult } = require('express-validator');
const { query } = require('../database/connection');
const { authenticateToken } = require('../middleware/auth');
const { registerDeviceToken, unregisterDeviceToken } = require('../services/notificationService');
const logger = require('../utils/logger');

const router = express.Router();

// Get user notifications
router.get('/', authenticateToken, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = Math.min(parseInt(req.query.limit) || 20, 100);
    const offset = (page - 1) * limit;

    const notificationsResult = await query(`
      SELECT 
        id, title, message, notification_type, related_id,
        is_read, sent_at, read_at
      FROM notifications
      WHERE user_id = $1
      ORDER BY sent_at DESC
      LIMIT $2 OFFSET $3
    `, [req.user.id, limit, offset]);

    const countResult = await query(
      'SELECT COUNT(*) FROM notifications WHERE user_id = $1',
      [req.user.id]
    );

    const total = parseInt(countResult.rows[0].count);
    const pages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: {
        notifications: notificationsResult.rows,
        pagination: { page, limit, total, pages }
      }
    });
  } catch (error) {
    logger.error('Get notifications error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Mark notification as read
router.put('/:id/read', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await query(`
      UPDATE notifications 
      SET is_read = true, read_at = CURRENT_TIMESTAMP
      WHERE id = $1 AND user_id = $2
    `, [id, req.user.id]);

    if (result.rowCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'Notification not found'
      });
    }

    res.json({
      success: true,
      message: 'Notification marked as read'
    });
  } catch (error) {
    logger.error('Mark notification read error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Register device for push notifications
router.post('/register-device', [
  authenticateToken,
  body('deviceToken').notEmpty(),
  body('platform').isIn(['ios', 'android'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { deviceToken, platform } = req.body;

    await registerDeviceToken(req.user.id, deviceToken, platform);

    res.json({
      success: true,
      message: 'Device registered for notifications'
    });
  } catch (error) {
    logger.error('Register device error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Unregister device
router.post('/unregister-device', [
  authenticateToken,
  body('deviceToken').notEmpty()
], async (req, res) => {
  try {
    const { deviceToken } = req.body;

    await unregisterDeviceToken(deviceToken);

    res.json({
      success: true,
      message: 'Device unregistered from notifications'
    });
  } catch (error) {
    logger.error('Unregister device error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
