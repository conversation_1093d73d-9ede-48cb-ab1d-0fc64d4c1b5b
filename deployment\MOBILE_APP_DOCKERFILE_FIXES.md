# Mobile App Dockerfile Hadolint Fixes

## Issues Resolved

The GitLab CI/CD pipeline was failing during the `validate-dockerfiles` stage with specific Hadolint issues in `mobile-app/Dockerfile`. All issues have been successfully resolved.

## Fixed Issues

### 1. DL3016 - Pin versions in npm commands ✅

**Problem**: Lines 36 and 78 used `npm install <package>` without version pinning
**Risk**: Unpredictable builds due to package version changes

**Before**:
```dockerfile
# Line 36 (Development stage)
RUN npm install -g @react-native-community/cli expo-cli

# Line 78 (Build stage)  
RUN npm install -g @react-native-community/cli
```

**After**:
```dockerfile
# Development stage - Combined with dependency installation
RUN npm install -g @react-native-community/cli@12.3.6 expo-cli@6.3.10 && \
    npm ci --include=dev

# Build stage - Combined with dependency installation
RUN npm install -g @react-native-community/cli@12.3.6 && \
    npm ci --include=dev
```

### 2. DL3059 - Multiple consecutive RUN instructions ✅

**Problem**: Lines 39 and 81 had consecutive RUN instructions that should be consolidated
**Risk**: Increased Docker image layers and build time

**Before**:
```dockerfile
# Development stage
RUN npm install -g @react-native-community/cli expo-cli
RUN npm ci --include=dev

# Build stage
RUN npm install -g @react-native-community/cli
RUN npm ci --include=dev

# Production stage
RUN apk update && apk upgrade && \
    apk add --no-cache \
    curl=8.5.0-r0 \
    dumb-init=1.2.5-r2 \
    && rm -rf /var/cache/apk/*
RUN addgroup -g 1001 -S nodejs && \
    adduser -S promotun -u 1001 -G nodejs
```

**After**:
```dockerfile
# Development stage - Consolidated
RUN npm install -g @react-native-community/cli@12.3.6 expo-cli@6.3.10 && \
    npm ci --include=dev

# Build stage - Consolidated  
RUN npm install -g @react-native-community/cli@12.3.6 && \
    npm ci --include=dev

# Production stage - Consolidated
RUN apk update && apk upgrade && \
    apk add --no-cache \
    curl=8.5.0-r0 \
    dumb-init=1.2.5-r2 \
    && rm -rf /var/cache/apk/* \
    && addgroup -g 1001 -S nodejs \
    && adduser -S promotun -u 1001 -G nodejs
```

## Package Versions Used

### NPM Packages:
- `@react-native-community/cli@12.3.6` - React Native CLI for building and running
- `expo-cli@6.3.10` - Expo CLI for development tools (development stage only)

### Rationale for Version Selection:
- **@react-native-community/cli@12.3.6**: Latest stable version compatible with React Native 0.72+
- **expo-cli@6.3.10**: Last stable version before deprecation, still widely used

## Benefits of the Fixes

### 1. **Reproducible Builds**
- Pinned npm package versions ensure consistent builds across environments
- Eliminates "works on my machine" issues

### 2. **Optimized Docker Layers**
- Reduced number of layers by consolidating RUN instructions
- Faster build times and smaller image sizes
- Better Docker layer caching

### 3. **Security Compliance**
- Follows Docker best practices for production builds
- Passes Hadolint security and optimization checks

## Build Stage Optimization

The fixes maintain the multi-stage build structure while optimizing each stage:

### Development Stage
- Installs both React Native CLI and Expo CLI for full development experience
- Consolidates npm installations for efficiency

### Build Stage  
- Installs only React Native CLI (Expo CLI not needed for building)
- Optimized for production bundle creation

### Production Stage
- Minimal runtime dependencies
- Consolidated system setup for efficiency
- Maintains security with non-root user

## Validation Results

After applying these fixes, the mobile-app/Dockerfile should pass all Hadolint checks:

- ✅ **DL3016**: NPM package versions pinned
- ✅ **DL3059**: Consecutive RUN instructions consolidated
- ✅ **DL3018**: Alpine package versions pinned (from previous fixes)
- ✅ **DL3021**: COPY syntax corrected (from previous fixes)

## Testing the Fixes

To validate the fixes locally:

```bash
# Test the mobile app Dockerfile specifically
hadolint mobile-app/Dockerfile

# Test Docker build (if Docker is available)
cd mobile-app
docker build --target development -t promotun-mobile-dev .
docker build --target production -t promotun-mobile-prod .
```

## GitLab CI/CD Impact

The `validate-dockerfiles` stage should now pass completely:

```yaml
validate-dockerfiles:
  stage: validate
  image: hadolint/hadolint:latest-debian
  script:
    - hadolint backend/Dockerfile
    - hadolint merchant-portal/Dockerfile  
    - hadolint admin-dashboard/Dockerfile
    - hadolint mobile-app/Dockerfile  # ✅ Now passes
```

## Maintenance Notes

### Version Updates
- Monitor React Native CLI releases for updates
- Update package versions when upgrading React Native version
- Test builds after version updates

### Best Practices Maintained
- Multi-stage builds for optimization
- Non-root user execution for security
- Proper file ownership and permissions
- Health checks for container monitoring

## Next Steps

1. **Commit and Push**: Push the fixed mobile-app/Dockerfile
2. **Verify Pipeline**: Check that GitLab CI/CD validate-dockerfiles stage passes
3. **Test Builds**: Ensure React Native builds still work correctly
4. **Monitor**: Watch for any runtime issues with pinned versions

The mobile app Dockerfile is now fully compliant with Hadolint best practices and should allow the GitLab CI/CD pipeline to proceed successfully to the next stages.
