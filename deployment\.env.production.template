# PromoTun Production Environment Variables Template
# Copy this file to .env.production and fill in the actual values
# DO NOT commit .env.production to version control

# ===================================
# Domain Configuration
# ===================================
DOMAIN_NAME=promodetect.com

# ===================================
# Database Configuration (External PostgreSQL VM)
# ===================================
DB_HOST=**************
DB_PORT=5432
DB_NAME=promotun_production
DB_USER=promotun_user
DB_PASSWORD=CHANGE_ME_SECURE_DB_PASSWORD
DB_SSL=true
DB_CONNECTION_TIMEOUT=30000
DB_POOL_MIN=2
DB_POOL_MAX=10

# ===================================
# Redis Configuration
# ===================================
REDIS_PASSWORD=CHANGE_ME_SECURE_REDIS_PASSWORD

# ===================================
# Security Secrets (Generate strong random values)
# ===================================
JWT_SECRET=CHANGE_ME_SECURE_JWT_SECRET_64_CHARS_MIN
SESSION_SECRET=CHANGE_ME_SECURE_SESSION_SECRET_64_CHARS_MIN
NEXTAUTH_SECRET=CHANGE_ME_SECURE_NEXTAUTH_SECRET_64_CHARS_MIN
BCRYPT_ROUNDS=12

# ===================================
# Firebase Configuration
# ===================================
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----"

# ===================================
# External API Keys
# ===================================
SENDGRID_API_KEY=SG.CHANGE_ME_SENDGRID_API_KEY
GOOGLE_MAPS_API_KEY=CHANGE_ME_GOOGLE_MAPS_API_KEY

# ===================================
# Email Configuration
# ===================================
FROM_EMAIL=<EMAIL>
FROM_NAME=PromoTun Platform

# ===================================
# Application Configuration
# ===================================
LOG_LEVEL=info
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# ===================================
# Monitoring Configuration
# ===================================
GRAFANA_PASSWORD=CHANGE_ME_SECURE_GRAFANA_PASSWORD

# ===================================
# Docker Image Tags (Set by CI/CD)
# ===================================
BACKEND_IMAGE=registry.gitlab.com/your-group/promotun-platform/backend:latest
MERCHANT_PORTAL_IMAGE=registry.gitlab.com/your-group/promotun-platform/merchant-portal:latest
ADMIN_DASHBOARD_IMAGE=registry.gitlab.com/your-group/promotun-platform/admin-dashboard:latest
MOBILE_APP_IMAGE=registry.gitlab.com/your-group/promotun-platform/mobile-app:latest

# ===================================
# Instructions for Generating Secure Values
# ===================================
# Generate secure passwords and secrets using:
# openssl rand -base64 64
# 
# For JWT_SECRET, SESSION_SECRET, NEXTAUTH_SECRET:
# - Use at least 64 characters
# - Include letters, numbers, and special characters
# - Keep them secret and never share
#
# For database and Redis passwords:
# - Use at least 32 characters
# - Include uppercase, lowercase, numbers, and symbols
# - Avoid common words or patterns
