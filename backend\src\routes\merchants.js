const express = require('express');
const { body, validationResult } = require('express-validator');
const { query } = require('../database/connection');
const { authenticateToken, requireMerchant } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Get merchant profile
router.get('/profile', authenticateToken, requireMerchant, async (req, res) => {
  try {
    const merchantResult = await query(`
      SELECT 
        mp.*, u.email, u.first_name, u.last_name, u.preferred_language
      FROM merchant_profiles mp
      JOIN users u ON mp.user_id = u.id
      WHERE mp.user_id = $1
    `, [req.user.id]);

    if (merchantResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Merchant profile not found'
      });
    }

    res.json({
      success: true,
      data: { merchant: merchantResult.rows[0] }
    });
  } catch (error) {
    logger.error('Get merchant profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Update merchant profile
router.put('/profile', [
  authenticateToken,
  requireMerchant,
  body('businessName').optional().trim().isLength({ min: 2, max: 255 }),
  body('businessType').optional().trim().isLength({ max: 100 }),
  body('description').optional().trim().isLength({ max: 2000 }),
  body('websiteUrl').optional().isURL()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const updateFields = req.body;
    const updateKeys = Object.keys(updateFields);
    const setClause = updateKeys.map((key, index) => `${key} = $${index + 2}`).join(', ');
    const values = [req.user.id, ...Object.values(updateFields)];

    const updatedMerchant = await query(`
      UPDATE merchant_profiles 
      SET ${setClause}, updated_at = CURRENT_TIMESTAMP
      WHERE user_id = $1
      RETURNING *
    `, values);

    res.json({
      success: true,
      message: 'Merchant profile updated successfully',
      data: { merchant: updatedMerchant.rows[0] }
    });
  } catch (error) {
    logger.error('Update merchant profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get merchant analytics
router.get('/analytics', authenticateToken, requireMerchant, async (req, res) => {
  try {
    const { startDate, endDate, period = 'day' } = req.query;

    // Get merchant ID
    const merchantResult = await query(
      'SELECT id FROM merchant_profiles WHERE user_id = $1',
      [req.user.id]
    );

    if (merchantResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Merchant profile not found'
      });
    }

    const merchantId = merchantResult.rows[0].id;

    // Get basic stats
    const statsResult = await query(`
      SELECT 
        COUNT(*) as total_promotions,
        COUNT(*) FILTER (WHERE is_active = true AND start_date <= CURRENT_TIMESTAMP AND end_date >= CURRENT_TIMESTAMP) as active_promotions,
        COUNT(*) FILTER (WHERE end_date < CURRENT_TIMESTAMP) as expired_promotions,
        AVG(discount_percentage) as avg_discount
      FROM promotions
      WHERE merchant_id = $1
    `, [merchantId]);

    // Get views and interactions (mock data for now)
    const analytics = {
      stats: statsResult.rows[0],
      views: {
        total: 1250,
        trend: '+12%'
      },
      favorites: {
        total: 89,
        trend: '+5%'
      },
      conversions: {
        total: 45,
        rate: 3.6,
        trend: '+8%'
      }
    };

    res.json({
      success: true,
      data: { analytics }
    });
  } catch (error) {
    logger.error('Get merchant analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
