import React from 'react';
import { NextPage } from 'next';
import { Box, Container, Grid, Paper, Typography, Card, CardContent } from '@mui/material';
import { useTranslation } from 'react-i18next';
import {
  TrendingUp,
  Visibility,
  ThumbUp,
  ShoppingCart,
  People,
  LocalOffer,
} from '@mui/icons-material';

import { useAuth } from '../store/authStore';
import DashboardCard from '../components/DashboardCard';
import PromotionChart from '../components/Dashboard/PromotionChart';
import RecentActivity from '../components/Dashboard/RecentActivity';
import QuickActions from '../components/Dashboard/QuickActions';
import { useDashboardData } from '../hooks/useDashboardData';

const Dashboard: NextPage = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { data: dashboardData, isLoading } = useDashboardData();

  if (isLoading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          {t('dashboard.loading')}
        </Typography>
      </Container>
    );
  }

  const stats = dashboardData?.stats || {};

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Welcome Section */}
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          {t('dashboard.welcome', { name: user?.firstName || 'Merchant' })}
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          {t('dashboard.subtitle')}
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <DashboardCard
            title={t('dashboard.stats.activePromotions')}
            value={stats.activePromotions || 0}
            icon={<LocalOffer />}
            color="primary"
            trend={stats.promotionsTrend}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <DashboardCard
            title={t('dashboard.stats.totalViews')}
            value={stats.totalViews || 0}
            icon={<Visibility />}
            color="info"
            trend={stats.viewsTrend}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <DashboardCard
            title={t('dashboard.stats.totalLikes')}
            value={stats.totalLikes || 0}
            icon={<ThumbUp />}
            color="success"
            trend={stats.likesTrend}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <DashboardCard
            title={t('dashboard.stats.conversionRate')}
            value={`${stats.conversionRate || 0}%`}
            icon={<TrendingUp />}
            color="warning"
            trend={stats.conversionTrend}
          />
        </Grid>
      </Grid>

      {/* Charts and Activity */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Typography variant="h6" gutterBottom>
              {t('dashboard.charts.promotionPerformance')}
            </Typography>
            <PromotionChart data={dashboardData?.chartData} />
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Typography variant="h6" gutterBottom>
              {t('dashboard.recentActivity')}
            </Typography>
            <RecentActivity activities={dashboardData?.recentActivities} />
          </Paper>
        </Grid>
      </Grid>

      {/* Quick Actions */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <QuickActions />
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              {t('dashboard.tips.title')}
            </Typography>
            <Box>
              <Typography variant="body2" color="text.secondary" paragraph>
                {t('dashboard.tips.tip1')}
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                {t('dashboard.tips.tip2')}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {t('dashboard.tips.tip3')}
              </Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Dashboard;
