version: '3.8'

services:
  # Redis Cache (containerized on application VM)
  redis:
    image: redis:7-alpine
    container_name: promotun-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "127.0.0.1:6379:6379"
    networks:
      - promotun-network
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Backend API
  backend:
    image: ${BACKEND_IMAGE:-promotun-backend:latest}
    build:
      context: ../backend
      dockerfile: Dockerfile
      target: production
    container_name: promotun-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 5000
      # External PostgreSQL Database Configuration
      DB_HOST: ${DB_HOST}
      DB_PORT: ${DB_PORT}
      DB_NAME: ${DB_NAME}
      DB_USER: ${DB_USER}
      DB_PASSWORD: ${DB_PASSWORD}
      DB_SSL: ${DB_SSL:-true}
      DB_CONNECTION_TIMEOUT: ${DB_CONNECTION_TIMEOUT:-30000}
      DB_POOL_MIN: ${DB_POOL_MIN:-2}
      DB_POOL_MAX: ${DB_POOL_MAX:-10}
      # Redis Configuration
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      # Security Configuration
      JWT_SECRET: ${JWT_SECRET}
      SESSION_SECRET: ${SESSION_SECRET}
      BCRYPT_ROUNDS: ${BCRYPT_ROUNDS:-12}
      # External APIs
      FIREBASE_PROJECT_ID: ${FIREBASE_PROJECT_ID}
      FIREBASE_CLIENT_EMAIL: ${FIREBASE_CLIENT_EMAIL}
      FIREBASE_PRIVATE_KEY: ${FIREBASE_PRIVATE_KEY}
      SENDGRID_API_KEY: ${SENDGRID_API_KEY}
      GOOGLE_MAPS_API_KEY: ${GOOGLE_MAPS_API_KEY}
      # Application Configuration
      FRONTEND_URL: https://${DOMAIN_NAME}
      FROM_EMAIL: ${FROM_EMAIL}
      FROM_NAME: ${FROM_NAME}
      # Rate Limiting
      RATE_LIMIT_WINDOW_MS: ${RATE_LIMIT_WINDOW_MS:-900000}
      RATE_LIMIT_MAX_REQUESTS: ${RATE_LIMIT_MAX_REQUESTS:-100}
      # Logging
      LOG_LEVEL: ${LOG_LEVEL:-info}
      LOG_FILE: /app/logs/app.log
      # Production Flags
      ENABLE_CORS: "true"
      ENABLE_SWAGGER: "false"
      MOCK_EXTERNAL_APIS: "false"
      MOCK_DATABASE: "false"
    ports:
      - "127.0.0.1:5000:5000"
    depends_on:
      - redis
    networks:
      - promotun-network
    volumes:
      - backend_uploads:/app/uploads
      - backend_logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Merchant Portal
  merchant-portal:
    image: ${MERCHANT_PORTAL_IMAGE:-promotun-merchant-portal:latest}
    build:
      context: ../merchant-portal
      dockerfile: Dockerfile
      target: production
    container_name: promotun-merchant-portal
    restart: unless-stopped
    environment:
      NODE_ENV: production
      NEXT_PUBLIC_API_URL: https://${DOMAIN_NAME}/api
      NEXT_PUBLIC_SOCKET_URL: https://${DOMAIN_NAME}
      NEXTAUTH_URL: https://${DOMAIN_NAME}
      NEXTAUTH_SECRET: ${NEXTAUTH_SECRET}
      NEXT_PUBLIC_GOOGLE_MAPS_API_KEY: ${GOOGLE_MAPS_API_KEY}
      NEXT_PUBLIC_DOMAIN: ${DOMAIN_NAME}
    ports:
      - "127.0.0.1:3000:3000"
    depends_on:
      - backend
    networks:
      - promotun-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Admin Dashboard
  admin-dashboard:
    image: ${ADMIN_DASHBOARD_IMAGE:-promotun-admin-dashboard:latest}
    build:
      context: ../admin-dashboard
      dockerfile: Dockerfile
      target: production
    container_name: promotun-admin-dashboard
    restart: unless-stopped
    environment:
      NODE_ENV: production
      NEXT_PUBLIC_API_URL: https://${DOMAIN_NAME}/api
      NEXT_PUBLIC_SOCKET_URL: https://${DOMAIN_NAME}
      NEXTAUTH_URL: https://${DOMAIN_NAME}/admin
      NEXTAUTH_SECRET: ${NEXTAUTH_SECRET}
    ports:
      - "127.0.0.1:3001:3000"
    depends_on:
      - backend
    networks:
      - promotun-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: promotun-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.production.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/sites-enabled:/etc/nginx/sites-enabled:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - backend_uploads:/var/www/uploads:ro
      - nginx_logs:/var/log/nginx
      # Create certbot webroot for Let's Encrypt
      - certbot_webroot:/var/www/certbot:ro
    depends_on:
      - backend
      - merchant-portal
      - admin-dashboard
    networks:
      - promotun-network
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: promotun-prometheus
    restart: unless-stopped
    ports:
      - "127.0.0.1:9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.external-url=https://${DOMAIN_NAME}/prometheus'
    networks:
      - promotun-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Grafana for Monitoring Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: promotun-grafana
    restart: unless-stopped
    ports:
      - "127.0.0.1:3002:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
      GF_SERVER_ROOT_URL: https://${DOMAIN_NAME}/grafana
      GF_SERVER_SERVE_FROM_SUB_PATH: "true"
      GF_SECURITY_COOKIE_SECURE: "true"
      GF_SECURITY_STRICT_TRANSPORT_SECURITY: "true"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - promotun-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

volumes:
  redis_data:
    driver: local
  backend_uploads:
    driver: local
  backend_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  nginx_logs:
    driver: local
  certbot_webroot:
    driver: local

networks:
  promotun-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
