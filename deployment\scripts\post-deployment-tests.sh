#!/bin/bash

# PromoTun Post-Deployment Tests
# Comprehensive testing after production deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="promodetect.com"
TIMEOUT=30
RETRY_COUNT=3

# Test results
TESTS_PASSED=0
TESTS_FAILED=0
FAILED_TESTS=()

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Test function wrapper
run_test() {
    local test_name="$1"
    local test_function="$2"
    
    print_info "Running test: $test_name"
    
    if $test_function; then
        print_status "PASS: $test_name"
        ((TESTS_PASSED++))
    else
        print_error "FAIL: $test_name"
        ((TESTS_FAILED++))
        FAILED_TESTS+=("$test_name")
    fi
    
    echo
}

# Test container health
test_container_health() {
    local containers=("promotun-backend" "promotun-merchant-portal" "promotun-admin-dashboard" "promotun-nginx" "promotun-redis")
    
    for container in "${containers[@]}"; do
        if docker ps --filter "name=$container" --filter "status=running" | grep -q "$container"; then
            # Check health status if health check is configured
            health_status=$(docker inspect --format='{{.State.Health.Status}}' "$container" 2>/dev/null || echo "no-healthcheck")
            
            if [[ "$health_status" == "healthy" ]] || [[ "$health_status" == "no-healthcheck" ]]; then
                continue
            else
                print_error "Container $container is not healthy: $health_status"
                return 1
            fi
        else
            print_error "Container $container is not running"
            return 1
        fi
    done
    
    return 0
}

# Test HTTP endpoints
test_http_endpoints() {
    local endpoints=(
        "http://localhost/health"
        "http://localhost/api/health"
        "http://localhost/api/categories"
        "http://localhost:3000"
        "http://localhost:3001"
    )
    
    for endpoint in "${endpoints[@]}"; do
        if ! curl -f -s --max-time $TIMEOUT "$endpoint" > /dev/null; then
            print_error "HTTP endpoint failed: $endpoint"
            return 1
        fi
    done
    
    return 0
}

# Test HTTPS endpoints
test_https_endpoints() {
    local endpoints=(
        "https://$DOMAIN/health"
        "https://$DOMAIN/api/health"
        "https://$DOMAIN/api/categories"
        "https://$DOMAIN"
    )
    
    for endpoint in "${endpoints[@]}"; do
        if ! curl -f -s --max-time $TIMEOUT "$endpoint" > /dev/null; then
            print_error "HTTPS endpoint failed: $endpoint"
            return 1
        fi
    done
    
    return 0
}

# Test SSL certificate
test_ssl_certificate() {
    # Check certificate validity
    if ! echo | openssl s_client -servername "$DOMAIN" -connect "$DOMAIN:443" 2>/dev/null | openssl x509 -noout -dates; then
        print_error "SSL certificate check failed"
        return 1
    fi
    
    # Check certificate expiration (warn if less than 30 days)
    expiry_date=$(echo | openssl s_client -servername "$DOMAIN" -connect "$DOMAIN:443" 2>/dev/null | openssl x509 -noout -enddate | cut -d= -f2)
    expiry_timestamp=$(date -d "$expiry_date" +%s)
    current_timestamp=$(date +%s)
    days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))
    
    if [[ $days_until_expiry -lt 30 ]]; then
        print_warning "SSL certificate expires in $days_until_expiry days"
    fi
    
    return 0
}

# Test database connectivity
test_database_connectivity() {
    # Source environment variables
    if [[ -f "/opt/promotun/deployment/.env.production" ]]; then
        source /opt/promotun/deployment/.env.production
        
        # Test database connection from backend container
        if docker exec promotun-backend sh -c "curl -f http://localhost:5000/api/health" | grep -q "database.*ok"; then
            return 0
        else
            print_error "Database connectivity test failed"
            return 1
        fi
    else
        print_error "Environment file not found"
        return 1
    fi
}

# Test Redis connectivity
test_redis_connectivity() {
    # Test Redis connection
    if docker exec promotun-redis redis-cli ping | grep -q "PONG"; then
        return 0
    else
        print_error "Redis connectivity test failed"
        return 1
    fi
}

# Test API functionality
test_api_functionality() {
    # Test API endpoints with actual functionality
    local api_tests=(
        "GET /api/categories"
        "GET /api/promotions"
        "GET /api/health"
    )
    
    for test in "${api_tests[@]}"; do
        method=$(echo $test | cut -d' ' -f1)
        endpoint=$(echo $test | cut -d' ' -f2)
        
        case $method in
            "GET")
                if ! curl -f -s --max-time $TIMEOUT "https://$DOMAIN$endpoint" | jq . > /dev/null 2>&1; then
                    print_error "API test failed: $test"
                    return 1
                fi
                ;;
        esac
    done
    
    return 0
}

# Test monitoring endpoints
test_monitoring() {
    # Test Prometheus metrics
    if ! curl -f -s --max-time $TIMEOUT "http://localhost:9090/metrics" > /dev/null; then
        print_error "Prometheus metrics endpoint failed"
        return 1
    fi
    
    # Test Grafana (if accessible)
    if ! curl -f -s --max-time $TIMEOUT "http://localhost:3002" > /dev/null; then
        print_warning "Grafana endpoint not accessible (may be auth-protected)"
    fi
    
    return 0
}

# Test performance
test_performance() {
    # Simple performance test
    local response_time=$(curl -o /dev/null -s -w '%{time_total}' "https://$DOMAIN/api/health")
    
    # Check if response time is reasonable (less than 2 seconds)
    if (( $(echo "$response_time > 2.0" | bc -l) )); then
        print_warning "API response time is slow: ${response_time}s"
    fi
    
    # Test concurrent requests
    for i in {1..5}; do
        curl -f -s --max-time $TIMEOUT "https://$DOMAIN/api/categories" > /dev/null &
    done
    wait
    
    return 0
}

# Test security headers
test_security_headers() {
    local security_headers=(
        "Strict-Transport-Security"
        "X-Content-Type-Options"
        "X-Frame-Options"
        "X-XSS-Protection"
    )
    
    for header in "${security_headers[@]}"; do
        if ! curl -I -s "https://$DOMAIN" | grep -i "$header" > /dev/null; then
            print_error "Security header missing: $header"
            return 1
        fi
    done
    
    return 0
}

# Test log rotation and disk space
test_system_health() {
    # Check disk space
    disk_usage=$(df /opt/promotun | tail -1 | awk '{print $5}' | sed 's/%//')
    
    if [[ $disk_usage -gt 80 ]]; then
        print_warning "Disk usage is high: ${disk_usage}%"
    fi
    
    # Check if logs are being rotated
    if [[ ! -f "/etc/logrotate.d/promotun" ]]; then
        print_warning "Log rotation not configured"
    fi
    
    return 0
}

# Generate test report
generate_report() {
    print_info "Test Report"
    echo "==========="
    echo
    
    echo "📊 Test Results:"
    echo "   Passed: $TESTS_PASSED"
    echo "   Failed: $TESTS_FAILED"
    echo "   Total: $((TESTS_PASSED + TESTS_FAILED))"
    echo
    
    if [[ $TESTS_FAILED -gt 0 ]]; then
        echo "❌ Failed Tests:"
        for test in "${FAILED_TESTS[@]}"; do
            echo "   - $test"
        done
        echo
    fi
    
    echo "🌐 Service URLs:"
    echo "   Main Site: https://$DOMAIN"
    echo "   API: https://$DOMAIN/api"
    echo "   Admin: https://$DOMAIN/admin"
    echo "   Monitoring: https://$DOMAIN/grafana"
    echo
    
    echo "📈 System Status:"
    echo "   Containers: $(docker ps --filter "name=promotun" | wc -l) running"
    echo "   Disk Usage: $(df /opt/promotun | tail -1 | awk '{print $5}')"
    echo "   Memory Usage: $(free -h | grep Mem | awk '{print $3"/"$2}')"
    echo
}

# Main execution
main() {
    print_info "Starting PromoTun post-deployment tests..."
    echo
    
    # Wait for services to stabilize
    print_info "Waiting for services to stabilize..."
    sleep 30
    
    # Run all tests
    run_test "Container Health" test_container_health
    run_test "HTTP Endpoints" test_http_endpoints
    run_test "HTTPS Endpoints" test_https_endpoints
    run_test "SSL Certificate" test_ssl_certificate
    run_test "Database Connectivity" test_database_connectivity
    run_test "Redis Connectivity" test_redis_connectivity
    run_test "API Functionality" test_api_functionality
    run_test "Monitoring" test_monitoring
    run_test "Performance" test_performance
    run_test "Security Headers" test_security_headers
    run_test "System Health" test_system_health
    
    # Generate report
    generate_report
    
    # Exit with appropriate code
    if [[ $TESTS_FAILED -eq 0 ]]; then
        print_status "All post-deployment tests passed! 🎉"
        exit 0
    else
        print_error "Some post-deployment tests failed!"
        exit 1
    fi
}

# Run main function
main "$@"
