#!/bin/bash

# PromoTun Staging Deployment Script
# Deploys specific commit SHA to staging environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMMIT_SHA=${1:-latest}
APP_DIR="/opt/promotun"
COMPOSE_FILE="docker-compose.staging.yml"
ENV_FILE=".env.staging"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running as promotun user
check_user() {
    if [[ $(whoami) != "promotun" ]]; then
        print_error "This script must be run as the promotun user"
        exit 1
    fi
}

# Update application code
update_code() {
    print_info "Updating application code to commit: ${COMMIT_SHA}"
    
    # Pull latest changes
    git fetch origin
    git checkout ${COMMIT_SHA}
    
    print_status "Code updated to commit: ${COMMIT_SHA}"
}

# Update Docker images
update_images() {
    print_info "Pulling Docker images for commit: ${COMMIT_SHA}"
    
    # Login to GitLab Container Registry
    echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
    
    # Pull images with specific commit SHA
    docker pull $CI_REGISTRY_IMAGE/backend:${COMMIT_SHA} || docker pull $CI_REGISTRY_IMAGE/backend:latest
    docker pull $CI_REGISTRY_IMAGE/merchant-portal:${COMMIT_SHA} || docker pull $CI_REGISTRY_IMAGE/merchant-portal:latest
    docker pull $CI_REGISTRY_IMAGE/admin-dashboard:${COMMIT_SHA} || docker pull $CI_REGISTRY_IMAGE/admin-dashboard:latest
    
    # Tag images for staging
    docker tag $CI_REGISTRY_IMAGE/backend:${COMMIT_SHA} promotun-backend:staging 2>/dev/null || \
    docker tag $CI_REGISTRY_IMAGE/backend:latest promotun-backend:staging
    
    docker tag $CI_REGISTRY_IMAGE/merchant-portal:${COMMIT_SHA} promotun-merchant-portal:staging 2>/dev/null || \
    docker tag $CI_REGISTRY_IMAGE/merchant-portal:latest promotun-merchant-portal:staging
    
    docker tag $CI_REGISTRY_IMAGE/admin-dashboard:${COMMIT_SHA} promotun-admin-dashboard:staging 2>/dev/null || \
    docker tag $CI_REGISTRY_IMAGE/admin-dashboard:latest promotun-admin-dashboard:staging
    
    print_status "Docker images updated"
}

# Create staging compose file
create_staging_compose() {
    print_info "Creating staging Docker Compose configuration..."
    
    # Copy production compose and modify for staging
    cp docker-compose.production.yml ${COMPOSE_FILE}
    
    # Update image references for staging
    sed -i 's/build:/# build:/g' ${COMPOSE_FILE}
    sed -i 's/context: ..\/backend/# context: ..\/backend/g' ${COMPOSE_FILE}
    sed -i 's/dockerfile: Dockerfile/# dockerfile: Dockerfile/g' ${COMPOSE_FILE}
    sed -i 's/target: production/# target: production/g' ${COMPOSE_FILE}
    
    # Add image references
    sed -i '/container_name: promotun-backend/a\    image: promotun-backend:staging' ${COMPOSE_FILE}
    sed -i '/container_name: promotun-merchant-portal/a\    image: promotun-merchant-portal:staging' ${COMPOSE_FILE}
    sed -i '/container_name: promotun-admin-dashboard/a\    image: promotun-admin-dashboard:staging' ${COMPOSE_FILE}
    
    # Update ports for staging (avoid conflicts)
    sed -i 's/"80:80"/"8080:80"/g' ${COMPOSE_FILE}
    sed -i 's/"443:443"/"8443:443"/g' ${COMPOSE_FILE}
    
    print_status "Staging compose file created"
}

# Deploy to staging
deploy_staging() {
    print_info "Deploying to staging environment..."
    
    # Stop existing staging containers
    docker-compose -f ${COMPOSE_FILE} down 2>/dev/null || true
    
    # Start staging environment
    docker-compose -f ${COMPOSE_FILE} up -d
    
    if [[ $? -eq 0 ]]; then
        print_status "Staging deployment successful"
    else
        print_error "Staging deployment failed"
        exit 1
    fi
}

# Wait for services to be ready
wait_for_services() {
    print_info "Waiting for staging services to be ready..."
    
    # Wait for backend health check
    for i in {1..30}; do
        if curl -f -s http://localhost:5000/health > /dev/null; then
            print_status "Backend service is ready"
            break
        fi
        
        if [[ $i -eq 30 ]]; then
            print_error "Backend service failed to start"
            exit 1
        fi
        
        sleep 10
    done
    
    # Wait for frontend services
    sleep 30
    
    print_status "All staging services are ready"
}

# Verify staging deployment
verify_deployment() {
    print_info "Verifying staging deployment..."
    
    # Check container status
    echo
    print_info "Container Status:"
    docker-compose -f ${COMPOSE_FILE} ps
    
    # Check service health
    echo
    print_info "Service Health Checks:"
    
    # Backend API
    if curl -f -s http://localhost:5000/health | grep -q "OK"; then
        print_status "Backend API: Healthy"
    else
        print_error "Backend API: Unhealthy"
    fi
    
    # Merchant Portal
    if curl -f -s http://localhost:3000 > /dev/null; then
        print_status "Merchant Portal: Accessible"
    else
        print_error "Merchant Portal: Not accessible"
    fi
    
    # Admin Dashboard
    if curl -f -s http://localhost:3001 > /dev/null; then
        print_status "Admin Dashboard: Accessible"
    else
        print_error "Admin Dashboard: Not accessible"
    fi
    
    # Nginx (staging ports)
    if curl -f -s http://localhost:8080 > /dev/null; then
        print_status "Nginx Proxy: Working"
    else
        print_error "Nginx Proxy: Not working"
    fi
}

# Show staging summary
show_summary() {
    print_info "Staging Deployment Summary"
    echo "=========================="
    echo
    
    echo "🌐 Staging URLs:"
    echo "   Main Site: http://localhost:8080"
    echo "   API: http://localhost:5000"
    echo "   Merchant Portal: http://localhost:3000"
    echo "   Admin Dashboard: http://localhost:3001"
    echo
    
    echo "📊 Service Status:"
    docker-compose -f ${COMPOSE_FILE} ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"
    echo
    
    echo "📋 Management Commands:"
    echo "   View logs: docker-compose -f ${COMPOSE_FILE} logs -f [service]"
    echo "   Restart service: docker-compose -f ${COMPOSE_FILE} restart [service]"
    echo "   Stop staging: docker-compose -f ${COMPOSE_FILE} down"
    echo
}

# Main execution
main() {
    print_info "Starting PromoTun staging deployment..."
    print_info "Commit SHA: ${COMMIT_SHA}"
    echo
    
    check_user
    update_code
    update_images
    create_staging_compose
    deploy_staging
    wait_for_services
    verify_deployment
    show_summary
    
    print_status "Staging deployment completed successfully! 🎉"
    echo
    print_info "Staging environment is ready for testing."
}

# Run main function
main "$@"
