#!/bin/bash

# PromoTun SSL Certificate Setup Script
# This script sets up SSL certificates using Let's Encrypt for promodetect.com

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="promodetect.com"
EMAIL="<EMAIL>"
WEBROOT="/var/www/certbot"
SSL_DIR="/opt/promotun/ssl"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "This script must be run as root for SSL certificate management."
        exit 1
    fi
}

# Verify domain DNS
verify_dns() {
    print_info "Verifying DNS configuration for ${DOMAIN}..."
    
    # Get current public IP
    PUBLIC_IP=$(curl -s ifconfig.me)
    
    # Check if domain points to this server
    DOMAIN_IP=$(dig +short ${DOMAIN} | tail -n1)
    
    if [[ "${DOMAIN_IP}" != "${PUBLIC_IP}" ]]; then
        print_warning "Domain ${DOMAIN} does not point to this server (${PUBLIC_IP})"
        print_warning "Current DNS points to: ${DOMAIN_IP}"
        print_warning "Please update your DNS records before continuing"
        
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        print_status "DNS configuration verified"
    fi
}

# Setup temporary nginx for certificate validation
setup_temp_nginx() {
    print_info "Setting up temporary nginx for certificate validation..."

    # Create webroot directory
    mkdir -p ${WEBROOT}

    # Create temporary nginx configuration
    tee /etc/nginx/sites-available/temp-ssl > /dev/null <<EOF
server {
    listen 80;
    server_name ${DOMAIN} www.${DOMAIN};

    location /.well-known/acme-challenge/ {
        root ${WEBROOT};
        try_files \$uri =404;
    }

    location / {
        return 200 'SSL Setup in Progress';
        add_header Content-Type text/plain;
    }
}
EOF

    # Disable PromoTun site temporarily
    if [[ -f /etc/nginx/sites-enabled/promodetect.com ]]; then
        mv /etc/nginx/sites-enabled/promodetect.com /etc/nginx/sites-available/promodetect.com.disabled
    fi

    # Enable temporary configuration
    ln -sf /etc/nginx/sites-available/temp-ssl /etc/nginx/sites-enabled/
    rm -f /etc/nginx/sites-enabled/default

    # Test and reload nginx
    nginx -t && systemctl reload nginx

    print_status "Temporary nginx configuration active"
}

# Obtain SSL certificate
obtain_certificate() {
    print_info "Obtaining SSL certificate from Let's Encrypt..."
    
    # Install certbot if not already installed
    apt update
    apt install -y certbot python3-certbot-nginx
    
    # Obtain certificate using webroot method
    certbot certonly \
        --webroot \
        --webroot-path=${WEBROOT} \
        --email ${EMAIL} \
        --agree-tos \
        --no-eff-email \
        --domains ${DOMAIN},www.${DOMAIN} \
        --non-interactive
    
    if [[ $? -eq 0 ]]; then
        print_status "SSL certificate obtained successfully"
    else
        print_error "Failed to obtain SSL certificate"
        exit 1
    fi
}

# Copy certificates to application directory
copy_certificates() {
    print_info "Copying certificates to application directory..."
    
    # Create SSL directory
    mkdir -p ${SSL_DIR}
    
    # Copy certificates
    cp /etc/letsencrypt/live/${DOMAIN}/fullchain.pem ${SSL_DIR}/${DOMAIN}.crt
    cp /etc/letsencrypt/live/${DOMAIN}/privkey.pem ${SSL_DIR}/${DOMAIN}.key
    
    # Set proper permissions
    chown -R promotun:promotun ${SSL_DIR}
    chmod 644 ${SSL_DIR}/${DOMAIN}.crt
    chmod 600 ${SSL_DIR}/${DOMAIN}.key
    
    print_status "Certificates copied to ${SSL_DIR}"
}

# Setup certificate renewal
setup_renewal() {
    print_info "Setting up automatic certificate renewal..."
    
    # Create renewal script
    tee /usr/local/bin/renew-ssl.sh > /dev/null <<EOF
#!/bin/bash

# PromoTun SSL Certificate Renewal Script
DOMAIN="${DOMAIN}"
SSL_DIR="${SSL_DIR}"

# Renew certificate
certbot renew --quiet --webroot --webroot-path=${WEBROOT}

# Copy renewed certificates
if [[ -f /etc/letsencrypt/live/\${DOMAIN}/fullchain.pem ]]; then
    cp /etc/letsencrypt/live/\${DOMAIN}/fullchain.pem \${SSL_DIR}/\${DOMAIN}.crt
    cp /etc/letsencrypt/live/\${DOMAIN}/privkey.pem \${SSL_DIR}/\${DOMAIN}.key
    
    # Set permissions
    chown promotun:promotun \${SSL_DIR}/\${DOMAIN}.*
    chmod 644 \${SSL_DIR}/\${DOMAIN}.crt
    chmod 600 \${SSL_DIR}/\${DOMAIN}.key
    
    # Reload nginx
    docker-compose -f /opt/promotun/docker-compose.production.yml restart nginx
    
    echo "SSL certificates renewed and nginx reloaded"
fi
EOF
    
    chmod +x /usr/local/bin/renew-ssl.sh
    
    # Setup cron job for renewal (runs twice daily)
    crontab -l 2>/dev/null | { cat; echo "0 */12 * * * /usr/local/bin/renew-ssl.sh >> /var/log/ssl-renewal.log 2>&1"; } | crontab -
    
    print_status "Automatic renewal configured"
}

# Generate DH parameters for enhanced security
generate_dhparam() {
    print_info "Generating DH parameters (this may take a while)..."
    
    openssl dhparam -out ${SSL_DIR}/dhparam.pem 2048
    chown promotun:promotun ${SSL_DIR}/dhparam.pem
    chmod 644 ${SSL_DIR}/dhparam.pem
    
    print_status "DH parameters generated"
}

# Create enhanced nginx SSL configuration
create_ssl_config() {
    print_info "Creating enhanced SSL configuration..."
    
    tee ${SSL_DIR}/ssl-params.conf > /dev/null <<EOF
# SSL Configuration for PromoTun
ssl_protocols TLSv1.2 TLSv1.3;
ssl_prefer_server_ciphers off;
ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;

# SSL Session Settings
ssl_session_timeout 1d;
ssl_session_cache shared:SSL:50m;
ssl_session_tickets off;

# DH Parameters
ssl_dhparam ${SSL_DIR}/dhparam.pem;

# OCSP Stapling
ssl_stapling on;
ssl_stapling_verify on;
ssl_trusted_certificate ${SSL_DIR}/${DOMAIN}.crt;

# Security Headers
add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
EOF
    
    chown promotun:promotun ${SSL_DIR}/ssl-params.conf
    
    print_status "SSL configuration created"
}

# Test SSL configuration
test_ssl() {
    print_info "Testing SSL configuration..."

    # Remove temporary nginx config
    rm -f /etc/nginx/sites-enabled/temp-ssl

    # Restore PromoTun site configuration
    if [[ -f /etc/nginx/sites-available/promodetect.com.disabled ]]; then
        mv /etc/nginx/sites-available/promodetect.com.disabled /etc/nginx/sites-enabled/promodetect.com
        print_info "PromoTun site configuration restored"
    fi

    # Test nginx configuration
    nginx -t && systemctl reload nginx

    # Test with curl
    sleep 5
    if curl -s -I https://${DOMAIN} | grep -q "HTTP/2 200"; then
        print_status "SSL configuration test passed"
    else
        print_warning "SSL test inconclusive - manual verification recommended"
    fi
}

# Main execution
main() {
    print_info "Starting SSL certificate setup for ${DOMAIN}..."
    echo
    
    check_root
    verify_dns
    setup_temp_nginx
    obtain_certificate
    copy_certificates
    setup_renewal
    generate_dhparam
    create_ssl_config
    test_ssl
    
    print_status "SSL certificate setup completed successfully!"
    echo
    print_info "SSL Certificate Details:"
    echo "Domain: ${DOMAIN}"
    echo "Certificate: ${SSL_DIR}/${DOMAIN}.crt"
    echo "Private Key: ${SSL_DIR}/${DOMAIN}.key"
    echo "DH Parameters: ${SSL_DIR}/dhparam.pem"
    echo "SSL Config: ${SSL_DIR}/ssl-params.conf"
    echo
    print_info "Next steps:"
    echo "1. Update nginx configuration to use SSL certificates"
    echo "2. Restart the application containers"
    echo "3. Test HTTPS access to your domain"
    echo "4. Configure HSTS and security headers"
    echo
    print_warning "Certificate will auto-renew. Check /var/log/ssl-renewal.log for renewal logs"
}

# Run main function
main "$@"
