# PromoTun API Documentation

## Overview
The PromoTun API provides endpoints for managing promotions, users, merchants, and real-time notifications. The API follows RESTful principles and uses JSON for data exchange.

## Base URL
```
Production: https://api.promotun.com
Development: http://localhost:5000/api
```

## Authentication
The API uses <PERSON>W<PERSON> (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Response Format
All API responses follow this format:

```json
{
  "success": true|false,
  "message": "Response message",
  "data": {}, // Response data (if applicable)
  "errors": [] // Validation errors (if applicable)
}
```

## Error Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `422` - Validation Error
- `500` - Internal Server Error

## Endpoints

### Authentication

#### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "John",
  "lastName": "Doe",
  "userType": "consumer|merchant",
  "preferredLanguage": "en|fr|ar"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "userType": "consumer",
      "preferredLanguage": "en",
      "isVerified": false
    },
    "token": "jwt-token"
  }
}
```

#### POST /auth/login
Authenticate user and get access token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### POST /auth/logout
Logout user and invalidate token.

**Headers:** `Authorization: Bearer <token>`

#### POST /auth/verify-email
Verify user email with verification token.

**Request Body:**
```json
{
  "token": "verification-token"
}
```

### Users

#### GET /users/profile
Get current user profile.

**Headers:** `Authorization: Bearer <token>`

#### PUT /users/profile
Update user profile.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "phone": "+1234567890",
  "preferredLanguage": "en"
}
```

#### GET /users/favorites
Get user's favorite promotions.

**Headers:** `Authorization: Bearer <token>`

#### POST /users/favorites
Add promotion to favorites.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "promotionId": "uuid"
}
```

#### DELETE /users/favorites/:promotionId
Remove promotion from favorites.

**Headers:** `Authorization: Bearer <token>`

### Promotions

#### GET /promotions
Get promotions with filtering and pagination.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 20)
- `category` (string): Category ID filter
- `lat` (number): Latitude for location-based search
- `lng` (number): Longitude for location-based search
- `radius` (number): Search radius in kilometers (default: 10)
- `search` (string): Search term
- `sortBy` (string): Sort field (date, distance, discount)
- `sortOrder` (string): Sort order (asc, desc)

**Response:**
```json
{
  "success": true,
  "data": {
    "promotions": [
      {
        "id": "uuid",
        "title": "50% Off Electronics",
        "description": "Amazing discount on all electronics",
        "originalPrice": 100.00,
        "discountedPrice": 50.00,
        "discountPercentage": 50,
        "startDate": "2023-07-01T00:00:00Z",
        "endDate": "2023-07-31T23:59:59Z",
        "merchant": {
          "id": "uuid",
          "businessName": "Tech Store",
          "logo": "https://example.com/logo.jpg"
        },
        "location": {
          "id": "uuid",
          "name": "Main Store",
          "address": "123 Main St",
          "latitude": 40.7128,
          "longitude": -74.0060
        },
        "category": {
          "id": "uuid",
          "name": "Electronics"
        },
        "images": [
          {
            "id": "uuid",
            "url": "https://example.com/image.jpg",
            "isPrimary": true
          }
        ],
        "isFavorite": false,
        "distance": 2.5
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "pages": 5
    }
  }
}
```

#### GET /promotions/:id
Get promotion details by ID.

**Headers:** `Authorization: Bearer <token>` (optional)

#### POST /promotions
Create new promotion (Merchant only).

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "title": "50% Off Electronics",
  "description": "Amazing discount on all electronics",
  "categoryId": "uuid",
  "locationId": "uuid",
  "originalPrice": 100.00,
  "discountedPrice": 50.00,
  "promotionType": "percentage",
  "startDate": "2023-07-01T00:00:00Z",
  "endDate": "2023-07-31T23:59:59Z",
  "termsConditions": "Terms and conditions",
  "maxRedemptions": 100,
  "images": ["base64-image-data"]
}
```

#### PUT /promotions/:id
Update promotion (Merchant only).

**Headers:** `Authorization: Bearer <token>`

#### DELETE /promotions/:id
Delete promotion (Merchant only).

**Headers:** `Authorization: Bearer <token>`

### Categories

#### GET /categories
Get all categories.

**Response:**
```json
{
  "success": true,
  "data": {
    "categories": [
      {
        "id": "uuid",
        "name": "Electronics",
        "nameFr": "Électronique",
        "nameAr": "إلكترونيات",
        "description": "Electronic devices and gadgets",
        "icon": "/icons/electronics.svg",
        "parentId": null,
        "children": []
      }
    ]
  }
}
```

### Merchants

#### GET /merchants/profile
Get merchant profile (Merchant only).

**Headers:** `Authorization: Bearer <token>`

#### PUT /merchants/profile
Update merchant profile (Merchant only).

**Headers:** `Authorization: Bearer <token>`

#### GET /merchants/analytics
Get merchant analytics (Merchant only).

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `startDate` (string): Start date (ISO format)
- `endDate` (string): End date (ISO format)
- `period` (string): Period (day, week, month, year)

### Locations

#### GET /merchants/locations
Get merchant locations (Merchant only).

**Headers:** `Authorization: Bearer <token>`

#### POST /merchants/locations
Add new location (Merchant only).

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "name": "Main Store",
  "address": "123 Main St",
  "city": "New York",
  "country": "USA",
  "postalCode": "10001",
  "latitude": 40.7128,
  "longitude": -74.0060,
  "phone": "+1234567890",
  "isPrimary": true
}
```

### Feedback

#### GET /promotions/:id/feedback
Get promotion feedback.

#### POST /promotions/:id/feedback
Add feedback to promotion.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "rating": 5,
  "commentType": "positive",
  "comment": "Great deal!",
  "isAnonymous": false
}
```

#### POST /feedback/:id/response
Respond to feedback (Merchant only).

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "response": "Thank you for your feedback!"
}
```

### Notifications

#### GET /notifications
Get user notifications.

**Headers:** `Authorization: Bearer <token>`

#### PUT /notifications/:id/read
Mark notification as read.

**Headers:** `Authorization: Bearer <token>`

#### POST /notifications/register-device
Register device for push notifications.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "deviceToken": "firebase-device-token",
  "platform": "ios|android"
}
```

## WebSocket Events

### Connection
Connect to WebSocket server:
```javascript
const socket = io('ws://localhost:5000', {
  auth: {
    token: 'your-jwt-token'
  }
});
```

### Events

#### join_location
Join location-based room for real-time updates.
```javascript
socket.emit('join_location', {
  lat: 40.7128,
  lng: -74.0060
});
```

#### new_promotion
Receive new promotion notifications.
```javascript
socket.on('new_promotion', (promotion) => {
  console.log('New promotion:', promotion);
});
```

#### promotion_updated
Receive promotion update notifications.
```javascript
socket.on('promotion_updated', (promotion) => {
  console.log('Promotion updated:', promotion);
});
```

## Rate Limiting
API requests are limited to 100 requests per 15-minute window per IP address.

## Pagination
List endpoints support pagination with the following parameters:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 100)

## Localization
The API supports multiple languages. Set the `Accept-Language` header:
```
Accept-Language: en|fr|ar
```

## File Uploads
File uploads use multipart/form-data with the following limits:
- Maximum file size: 10MB
- Allowed types: JPEG, PNG, GIF, WebP
- Maximum files per request: 5
