import React, { createContext, useContext, useEffect } from 'react';
import { create } from 'zustand';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { showMessage } from 'react-native-flash-message';

import { authAPI } from '../services/api';
import { i18n } from '../locales/i18n';

// Zustand store for authentication
const useAuthStore = create((set, get) => ({
  user: null,
  token: null,
  isLoading: true,
  isAuthenticated: false,

  // Initialize auth state from storage
  initializeAuth: async () => {
    try {
      const token = await SecureStore.getItemAsync('authToken');
      const userData = await AsyncStorage.getItem('userData');
      
      if (token && userData) {
        const user = JSON.parse(userData);
        set({ 
          user, 
          token, 
          isAuthenticated: true, 
          isLoading: false 
        });
        
        // Set default language
        if (user.preferredLanguage) {
          i18n.changeLanguage(user.preferredLanguage);
        }
      } else {
        set({ isLoading: false });
      }
    } catch (error) {
      console.error('Error initializing auth:', error);
      set({ isLoading: false });
    }
  },

  // Login function
  login: async (email, password) => {
    try {
      const response = await authAPI.login(email, password);
      
      if (response.success) {
        const { user, token } = response.data;
        
        // Store token securely
        await SecureStore.setItemAsync('authToken', token);
        await AsyncStorage.setItem('userData', JSON.stringify(user));
        
        set({ 
          user, 
          token, 
          isAuthenticated: true 
        });
        
        // Set language preference
        if (user.preferredLanguage) {
          i18n.changeLanguage(user.preferredLanguage);
        }
        
        showMessage({
          message: 'Login Successful',
          description: 'Welcome back!',
          type: 'success',
        });
        
        return { success: true };
      } else {
        showMessage({
          message: 'Login Failed',
          description: response.message,
          type: 'danger',
        });
        return { success: false, message: response.message };
      }
    } catch (error) {
      const message = error.response?.data?.message || 'Login failed. Please try again.';
      showMessage({
        message: 'Login Error',
        description: message,
        type: 'danger',
      });
      return { success: false, message };
    }
  },

  // Register function
  register: async (userData) => {
    try {
      const response = await authAPI.register(userData);
      
      if (response.success) {
        const { user, token } = response.data;
        
        // Store token securely
        await SecureStore.setItemAsync('authToken', token);
        await AsyncStorage.setItem('userData', JSON.stringify(user));
        
        set({ 
          user, 
          token, 
          isAuthenticated: true 
        });
        
        // Set language preference
        if (user.preferredLanguage) {
          i18n.changeLanguage(user.preferredLanguage);
        }
        
        showMessage({
          message: 'Registration Successful',
          description: 'Please check your email for verification.',
          type: 'success',
        });
        
        return { success: true };
      } else {
        showMessage({
          message: 'Registration Failed',
          description: response.message,
          type: 'danger',
        });
        return { success: false, message: response.message };
      }
    } catch (error) {
      const message = error.response?.data?.message || 'Registration failed. Please try again.';
      showMessage({
        message: 'Registration Error',
        description: message,
        type: 'danger',
      });
      return { success: false, message };
    }
  },

  // Logout function
  logout: async () => {
    try {
      const { token } = get();
      
      if (token) {
        await authAPI.logout();
      }
      
      // Clear stored data
      await SecureStore.deleteItemAsync('authToken');
      await AsyncStorage.removeItem('userData');
      
      set({ 
        user: null, 
        token: null, 
        isAuthenticated: false 
      });
      
      showMessage({
        message: 'Logged Out',
        description: 'You have been successfully logged out.',
        type: 'info',
      });
    } catch (error) {
      console.error('Logout error:', error);
      // Still clear local data even if API call fails
      await SecureStore.deleteItemAsync('authToken');
      await AsyncStorage.removeItem('userData');
      
      set({ 
        user: null, 
        token: null, 
        isAuthenticated: false 
      });
    }
  },

  // Update user data
  updateUser: async (userData) => {
    try {
      const currentUser = get().user;
      const updatedUser = { ...currentUser, ...userData };
      
      await AsyncStorage.setItem('userData', JSON.stringify(updatedUser));
      set({ user: updatedUser });
      
      // Update language if changed
      if (userData.preferredLanguage && userData.preferredLanguage !== currentUser.preferredLanguage) {
        i18n.changeLanguage(userData.preferredLanguage);
      }
      
      return { success: true };
    } catch (error) {
      console.error('Error updating user:', error);
      return { success: false, message: 'Failed to update user data' };
    }
  },

  // Verify email
  verifyEmail: async (token) => {
    try {
      const response = await authAPI.verifyEmail(token);
      
      if (response.success) {
        const currentUser = get().user;
        const updatedUser = { ...currentUser, isVerified: true };
        
        await AsyncStorage.setItem('userData', JSON.stringify(updatedUser));
        set({ user: updatedUser });
        
        showMessage({
          message: 'Email Verified',
          description: 'Your email has been successfully verified.',
          type: 'success',
        });
        
        return { success: true };
      } else {
        showMessage({
          message: 'Verification Failed',
          description: response.message,
          type: 'danger',
        });
        return { success: false, message: response.message };
      }
    } catch (error) {
      const message = error.response?.data?.message || 'Verification failed. Please try again.';
      showMessage({
        message: 'Verification Error',
        description: message,
        type: 'danger',
      });
      return { success: false, message };
    }
  },

  // Get auth header for API calls
  getAuthHeader: () => {
    const { token } = get();
    return token ? { Authorization: `Bearer ${token}` } : {};
  },
}));

// Context provider for auth store
const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const authStore = useAuthStore();

  useEffect(() => {
    authStore.initializeAuth();
  }, []);

  return (
    <AuthContext.Provider value={authStore}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook to use auth store
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export { useAuthStore };
