#!/bin/bash

# PromoTun Project Setup Script
# This script sets up the development environment for PromoTun

set -e

echo "🚀 PromoTun Project Setup"
echo "========================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Node.js is installed
check_nodejs() {
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_status "Node.js is installed: $NODE_VERSION"
        
        # Check if version is 16 or higher
        NODE_MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [ "$NODE_MAJOR_VERSION" -lt 16 ]; then
            print_warning "Node.js version 16 or higher is recommended"
        fi
    else
        print_error "Node.js is not installed. Please install Node.js 16+ from https://nodejs.org/"
        exit 1
    fi
}

# Check if npm is installed
check_npm() {
    if command -v npm &> /dev/null; then
        NPM_VERSION=$(npm --version)
        print_status "npm is installed: $NPM_VERSION"
    else
        print_error "npm is not installed"
        exit 1
    fi
}

# Check if Docker is installed
check_docker() {
    if command -v docker &> /dev/null; then
        DOCKER_VERSION=$(docker --version)
        print_status "Docker is installed: $DOCKER_VERSION"
    else
        print_warning "Docker is not installed. Install Docker Desktop for full deployment capabilities"
    fi
}

# Setup environment files
setup_env_files() {
    print_info "Setting up environment files..."
    
    # Backend environment
    if [ ! -f "backend/.env" ]; then
        cp backend/.env.example backend/.env
        print_status "Created backend/.env from template"
    else
        print_warning "backend/.env already exists"
    fi
    
    # Merchant portal environment
    if [ ! -f "merchant-portal/.env.local" ]; then
        cp merchant-portal/.env.example merchant-portal/.env.local
        print_status "Created merchant-portal/.env.local from template"
    else
        print_warning "merchant-portal/.env.local already exists"
    fi
    
    # Deployment environment
    if [ ! -f "deployment/.env" ]; then
        cp deployment/.env.example deployment/.env
        print_status "Created deployment/.env from template"
    else
        print_warning "deployment/.env already exists"
    fi
}

# Install dependencies
install_dependencies() {
    print_info "Installing dependencies..."
    
    # Backend dependencies
    print_info "Installing backend dependencies..."
    cd backend
    npm install
    cd ..
    print_status "Backend dependencies installed"
    
    # Merchant portal dependencies
    print_info "Installing merchant portal dependencies..."
    cd merchant-portal
    npm install
    cd ..
    print_status "Merchant portal dependencies installed"
    
    # Admin dashboard dependencies
    if [ -f "admin-dashboard/package.json" ]; then
        print_info "Installing admin dashboard dependencies..."
        cd admin-dashboard
        npm install
        cd ..
        print_status "Admin dashboard dependencies installed"
    fi
}

# Create necessary directories
create_directories() {
    print_info "Creating necessary directories..."
    
    mkdir -p backend/logs
    mkdir -p backend/uploads
    mkdir -p data
    
    print_status "Directories created"
}

# Main setup function
main() {
    echo
    print_info "Checking prerequisites..."
    check_nodejs
    check_npm
    check_docker
    
    echo
    print_info "Setting up project..."
    setup_env_files
    create_directories
    install_dependencies
    
    echo
    print_status "Setup completed successfully!"
    echo
    print_info "Next steps:"
    echo "1. Edit environment files with your configuration:"
    echo "   - backend/.env"
    echo "   - merchant-portal/.env.local"
    echo "   - deployment/.env"
    echo
    echo "2. Start the development servers:"
    echo "   Backend:        cd backend && npm start"
    echo "   Merchant Portal: cd merchant-portal && npm run dev"
    echo
    echo "3. Or use Docker for full deployment:"
    echo "   cd deployment && docker-compose up -d"
    echo
    print_status "Happy coding! 🎉"
}

# Run main function
main
